"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_components_home_ProductCategories_tsx",{

/***/ "(app-pages-browser)/./components/home/<USER>":
/*!***********************************************!*\
  !*** ./components/home/<USER>
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./components/ui/skeleton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst ProductCategories = (param)=>{\n    let { categories, title = \"Shop by Category\", subtitle, accentColor = \"primary\", variant = \"section\", showTitle = true, showViewAll = true, maxCategories = 12 } = param;\n    _s();\n    const pathName = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [isLoading, setIsLoading] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(true);\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ProductCategories.useEffect\": ()=>{\n            // Set loading to false when categories are available\n            if (categories && Array.isArray(categories)) {\n                setIsLoading(false);\n            }\n        }\n    }[\"ProductCategories.useEffect\"], [\n        categories\n    ]);\n    // Default categories if none are provided or if the array is empty\n    const defaultCategories = [\n        {\n            id: 1,\n            name: \"Smart Locks\",\n            slug: \"smart-locks\",\n            image_url: \"https://placehold.co/400x400/2ECC71/FFFFFF?text=Smart+Locks\"\n        },\n        {\n            id: 2,\n            name: \"Security Cameras\",\n            slug: \"security-cameras\",\n            image_url: \"https://placehold.co/400x400/3498DB/FFFFFF?text=Security+Cameras\"\n        },\n        {\n            id: 3,\n            name: \"Home Automation\",\n            slug: \"home-automation\",\n            image_url: \"https://placehold.co/400x400/9B59B6/FFFFFF?text=Home+Automation\"\n        },\n        {\n            id: 4,\n            name: \"Lighting\",\n            slug: \"lighting\",\n            image_url: \"https://placehold.co/400x400/F1C40F/FFFFFF?text=Lighting\"\n        },\n        {\n            id: 5,\n            name: \"Sensors\",\n            slug: \"sensors\",\n            image_url: \"https://placehold.co/400x400/E74C3C/FFFFFF?text=Sensors\"\n        },\n        {\n            id: 6,\n            name: \"Alarms\",\n            slug: \"alarms\",\n            image_url: \"https://placehold.co/400x400/1ABC9C/FFFFFF?text=Alarms\"\n        }\n    ];\n    // Show skeleton loader when categories are loading\n    if (isLoading && variant === \"navigation\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"w-full bg-white/95 backdrop-blur-sm border-b border-gray-100 shadow-sm\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-3 sm:px-4 lg:px-6 py-3 sm:py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"block md:hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-4 overflow-x-auto scrollbar-hide pb-2 -mx-3 px-3\",\n                            children: Array.from({\n                                length: 8\n                            }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 flex flex-col items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 rounded-2xl bg-gray-200 animate-pulse mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__.Skeleton, {\n                                            className: \"w-12 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:block space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-8 lg:grid-cols-10 xl:grid-cols-12 gap-3 lg:gap-4\",\n                                children: Array.from({\n                                    length: 12\n                                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center fade-in-professional\",\n                                        style: {\n                                            animationDelay: \"\".concat(index * 0.05, \"s\")\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 lg:w-18 lg:h-18 xl:w-20 xl:h-20 rounded-2xl skeleton-professional mb-2 border border-gray-100/80 shadow-md\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 lg:w-14 xl:w-16 h-3 rounded skeleton-professional\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 h-3 rounded skeleton-professional opacity-60\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 h-px bg-gray-200\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2 overflow-x-auto scrollbar-hide pb-1 -mx-1 px-1\",\n                                        children: Array.from({\n                                            length: 8\n                                        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 flex flex-col items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 lg:w-14 lg:h-14 rounded-xl skeleton-professional mb-1 border border-gray-100/60 shadow-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 lg:w-10 h-2 rounded skeleton-professional\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Don't render navigation if no categories\n    if (!categories || !Array.isArray(categories) || categories.length === 0) {\n        if (variant === \"navigation\") {\n            return null;\n        }\n    }\n    // Use provided categories if available, otherwise use default categories\n    // Make sure we have a valid array to work with\n    const effectiveCategories = Array.isArray(categories) && categories.length > 0 ? categories : defaultCategories;\n    // Process categories to ensure proper image handling\n    const categoriesWithImages = effectiveCategories.map((category)=>{\n        // Use image_url from backend API if available, otherwise fallback to image field or generate placeholder\n        let imageUrl = category.image_url || category.image;\n        // If no image is available from the backend, generate a colored placeholder immediately\n        if (!imageUrl) {\n            const colors = [\n                '2ECC71',\n                '3498DB',\n                '9B59B6',\n                'F1C40F',\n                'E74C3C',\n                '1ABC9C',\n                'E67E22',\n                '34495E',\n                '95A5A6',\n                'F39C12',\n                'D35400',\n                '8E44AD'\n            ];\n            const colorIndex = category.id % colors.length;\n            const color = colors[colorIndex];\n            const categoryText = encodeURIComponent(category.name);\n            imageUrl = \"https://placehold.co/400x400/\".concat(color, \"/FFFFFF?text=\").concat(categoryText);\n        }\n        return {\n            ...category,\n            image_url: imageUrl,\n            // Add a fallback image path for onError handler\n            fallbackImage: \"/assets/products/product-placeholder.svg\"\n        };\n    });\n    // Determine accent color classes\n    const accentClasses = {\n        primary: {\n            bg: \"bg-theme-accent-primary/20\",\n            text: \"text-theme-accent-primary\",\n            line: \"bg-theme-accent-primary\",\n            gradient: \"from-theme-accent-primary/5\",\n            activeBg: \"bg-theme-accent-primary\",\n            activeText: \"text-white\",\n            hoverBg: \"hover:bg-theme-accent-primary/10\"\n        },\n        secondary: {\n            bg: \"bg-theme-accent-secondary/30\",\n            text: \"text-theme-accent-secondary\",\n            line: \"bg-theme-accent-secondary\",\n            gradient: \"from-theme-accent-secondary/5\",\n            activeBg: \"bg-theme-accent-secondary\",\n            activeText: \"text-white\",\n            hoverBg: \"hover:bg-theme-accent-secondary/10\"\n        },\n        tertiary: {\n            bg: \"bg-theme-accent-primary/30\",\n            text: \"text-theme-accent-primary\",\n            line: \"bg-theme-accent-primary\",\n            gradient: \"from-theme-accent-primary/10\",\n            activeBg: \"bg-theme-accent-primary\",\n            activeText: \"text-white\",\n            hoverBg: \"hover:bg-theme-accent-primary/10\"\n        }\n    };\n    // Animation variants\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        show: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        show: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.5\n            }\n        }\n    };\n    // Navigation variant - Professional Amazon/Flipkart style layout\n    if (variant === \"navigation\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"w-full bg-white/98 backdrop-blur-md  shadow-sm relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-theme-accent-primary via-theme-accent-secondary to-theme-accent-primary opacity-60\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-3 sm:px-4 lg:px-6 xl:px-8 py-3 sm:py-4 lg:py-5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"block md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4 overflow-x-auto scrollbar-hide pb-2 -mx-3 px-3\",\n                                children: categoriesWithImages.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 group\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/products/categories/\".concat(category.slug, \"?callbackUrl=%2F\").concat(pathName),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center transition-all duration-300 group-active:scale-95\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative overflow-hidden rounded-2xl w-16 h-16 bg-gradient-to-br from-gray-50 to-gray-100 shadow-lg transition-all duration-300 group-hover:shadow-xl mb-2 border border-gray-200/50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: category.image_url,\n                                                                alt: category.name,\n                                                                className: \"absolute inset-0 w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\",\n                                                                onError: (e)=>{\n                                                                    const imgElement = e.target;\n                                                                    if (imgElement.src.includes('placehold.co')) {\n                                                                        return;\n                                                                    }\n                                                                    const colors = [\n                                                                        '2ECC71',\n                                                                        '3498DB',\n                                                                        '9B59B6',\n                                                                        'F1C40F',\n                                                                        'E74C3C',\n                                                                        '1ABC9C',\n                                                                        'E67E22',\n                                                                        '34495E',\n                                                                        '95A5A6',\n                                                                        'F39C12',\n                                                                        'D35400',\n                                                                        '8E44AD'\n                                                                    ];\n                                                                    const colorIndex = category.id % colors.length;\n                                                                    const color = colors[colorIndex];\n                                                                    const categoryText = encodeURIComponent(category.name);\n                                                                    imgElement.src = \"https://placehold.co/400x400/\".concat(color, \"/FFFFFF?text=\").concat(categoryText);\n                                                                    imgElement.onerror = null;\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 bg-gradient-to-t from-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium text-gray-700 text-center line-clamp-2 max-w-[4.5rem] leading-tight\",\n                                                        children: category.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, category.id || \"category-\".concat(index), false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:block space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-8 lg:grid-cols-10 xl:grid-cols-12 gap-3 lg:gap-4\",\n                                    children: categoriesWithImages.slice(0, 12).map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"group fade-in-professional\",\n                                            style: {\n                                                animationDelay: \"\".concat(index * 0.05, \"s\")\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/products/categories/\".concat(category.slug, \"?callbackUrl=%2F\").concat(pathName),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center transition-all duration-300 group-hover:-translate-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative overflow-hidden rounded-2xl w-16 h-16 lg:w-18 lg:h-18 xl:w-20 xl:h-20 bg-white/10 backdrop-blur-sm shadow-md group-hover:shadow-lg mb-2 border border-white/20 group-hover:border-blue-400/30 transition-all duration-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-1 rounded-xl overflow-hidden\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: category.image_url,\n                                                                        alt: category.name,\n                                                                        className: \"w-full h-full object-cover transition-transform duration-300 group-hover:scale-110\",\n                                                                        onError: (e)=>{\n                                                                            const imgElement = e.target;\n                                                                            if (imgElement.src.includes('placehold.co')) {\n                                                                                return;\n                                                                            }\n                                                                            const colors = [\n                                                                                '2ECC71',\n                                                                                '3498DB',\n                                                                                '9B59B6',\n                                                                                'F1C40F',\n                                                                                'E74C3C',\n                                                                                '1ABC9C',\n                                                                                'E67E22',\n                                                                                '34495E',\n                                                                                '95A5A6',\n                                                                                'F39C12',\n                                                                                'D35400',\n                                                                                '8E44AD'\n                                                                            ];\n                                                                            const colorIndex = category.id % colors.length;\n                                                                            const color = colors[colorIndex];\n                                                                            const categoryText = encodeURIComponent(category.name);\n                                                                            imgElement.src = \"https://placehold.co/400x400/\".concat(color, \"/FFFFFF?text=\").concat(categoryText);\n                                                                            imgElement.onerror = null;\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                        lineNumber: 290,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-gradient-to-t from-blue-500/20 via-transparent to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                    lineNumber: 312,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium text-white group-hover:text-blue-400 transition-colors duration-300 text-center line-clamp-2 max-w-[4rem] lg:max-w-[4.5rem] xl:max-w-[5rem] leading-tight\",\n                                                            children: category.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, category.id || \"category-\".concat(index), false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, undefined),\n                                categoriesWithImages.length > 12 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-medium text-white/70 uppercase tracking-wide\",\n                                                    children: \"More Categories\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 h-px bg-gradient-to-r from-white/20 to-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2 overflow-x-auto scrollbar-hide pb-1 -mx-1 px-1\",\n                                            children: categoriesWithImages.slice(12).map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 group\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/products/categories/\".concat(category.slug, \"?callbackUrl=%2F\").concat(pathName),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col items-center transition-all duration-300 group-hover:-translate-y-0.5\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative overflow-hidden rounded-xl w-12 h-12 lg:w-14 lg:h-14 bg-white shadow-sm group-hover:shadow-md mb-1 border border-gray-100/60 group-hover:border-theme-accent-primary/40 transition-all duration-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute inset-0.5 rounded-lg overflow-hidden\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: category.image_url,\n                                                                                alt: category.name,\n                                                                                className: \"w-full h-full object-cover transition-transform duration-300 group-hover:scale-110\",\n                                                                                onError: (e)=>{\n                                                                                    const imgElement = e.target;\n                                                                                    if (imgElement.src.includes('placehold.co')) {\n                                                                                        return;\n                                                                                    }\n                                                                                    const colors = [\n                                                                                        '2ECC71',\n                                                                                        '3498DB',\n                                                                                        '9B59B6',\n                                                                                        'F1C40F',\n                                                                                        'E74C3C',\n                                                                                        '1ABC9C',\n                                                                                        'E67E22',\n                                                                                        '34495E',\n                                                                                        '95A5A6',\n                                                                                        'F39C12',\n                                                                                        'D35400',\n                                                                                        '8E44AD'\n                                                                                    ];\n                                                                                    const colorIndex = category.id % colors.length;\n                                                                                    const color = colors[colorIndex];\n                                                                                    const categoryText = encodeURIComponent(category.name);\n                                                                                    imgElement.src = \"https://placehold.co/400x400/\".concat(color, \"/FFFFFF?text=\").concat(categoryText);\n                                                                                    imgElement.onerror = null;\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                                lineNumber: 339,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                            lineNumber: 338,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute inset-0 bg-theme-accent-primary/15 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                            lineNumber: 360,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-600 group-hover:text-theme-accent-primary transition-colors duration-300 text-center line-clamp-2 max-w-[3rem] lg:max-w-[3.5rem] leading-tight\",\n                                                                    children: category.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, category.id || \"category-more-\".concat(index), false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n            lineNumber: 229,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Section variant - full layout with background and decorations\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-12 sm:py-16 md:py-20 relative overflow-hidden w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-black/20 backdrop-blur-sm z-0\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                lineNumber: 382,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-1/3 right-[15%] w-32 h-32 rounded-full bg-purple-500/20 blur-xl opacity-50\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                lineNumber: 385,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-1/3 left-[10%] w-24 h-24 rounded-full bg-blue-500/20 blur-xl opacity-50\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                lineNumber: 386,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 w-full max-w-full 2xl:max-w-[1536px]\",\n                children: [\n                    showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center mb-8 sm:mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-3 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"relative z-10\",\n                                        children: title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"absolute -bottom-1 left-0 right-0 h-3 bg-gradient-to-r from-blue-500 to-purple-500 transform -rotate-1 z-0\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 13\n                            }, undefined),\n                            subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/80 text-center max-w-2xl mb-4\",\n                                children: subtitle\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 26\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 sm:w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mt-1\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        whileInView: \"show\",\n                        viewport: {\n                            once: true,\n                            margin: \"-100px\"\n                        },\n                        className: \"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 sm:gap-6\",\n                        children: categoriesWithImages.slice(0, maxCategories).map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                variants: itemVariants,\n                                className: \"group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/products/categories/\".concat(category.slug),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative overflow-hidden rounded-xl aspect-square bg-gray-100 shadow-md transition-all duration-300 group-hover:shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-b from-transparent to-black/50 opacity-70 z-10\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: category.image_url,\n                                                alt: category.name,\n                                                className: \"absolute inset-0 w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\",\n                                                onError: (e)=>{\n                                                    // Fallback to a colored placeholder if the backend image fails\n                                                    const imgElement = e.target;\n                                                    // Prevent infinite error loops\n                                                    if (imgElement.src.includes('placehold.co')) {\n                                                        return;\n                                                    }\n                                                    // Generate a colored placeholder based on category name\n                                                    const colors = [\n                                                        '2ECC71',\n                                                        '3498DB',\n                                                        '9B59B6',\n                                                        'F1C40F',\n                                                        'E74C3C',\n                                                        '1ABC9C',\n                                                        'E67E22',\n                                                        '34495E',\n                                                        '95A5A6',\n                                                        'F39C12',\n                                                        'D35400',\n                                                        '8E44AD'\n                                                    ];\n                                                    const colorIndex = category.id % colors.length;\n                                                    const color = colors[colorIndex];\n                                                    const categoryText = encodeURIComponent(category.name);\n                                                    imgElement.src = \"https://placehold.co/400x400/\".concat(color, \"/FFFFFF?text=\").concat(categoryText);\n                                                    imgElement.onerror = null; // Prevent further error handling\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-x-0 bottom-0 p-3 z-20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-white font-medium text-sm sm:text-base text-shadow\",\n                                                    children: category.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-theme-accent-primary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, category.id || \"category-\".concat(index), false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 9\n                    }, undefined),\n                    showViewAll && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/shop\",\n                            className: \"flex items-center \".concat(accentClasses[accentColor].text, \" hover:text-theme-accent-hover group transition-all duration-300\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"View All Categories\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform duration-300\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                        lineNumber: 462,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                lineNumber: 388,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n        lineNumber: 380,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProductCategories, \"+vsBosugv8Dy5voOOGzutsaeQmA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname\n    ];\n});\n_c = ProductCategories;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductCategories);\nvar _c;\n$RefreshReg$(_c, \"ProductCategories\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/home/<USER>"));

/***/ })

});