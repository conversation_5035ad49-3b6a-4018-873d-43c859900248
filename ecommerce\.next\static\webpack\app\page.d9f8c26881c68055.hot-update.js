"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hooks/useApi */ \"(app-pages-browser)/./hooks/useApi.ts\");\n/* harmony import */ var _layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../layout/MainHOF */ \"(app-pages-browser)/./layout/MainHOF.tsx\");\n/* harmony import */ var swiper_css_autoplay__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! swiper/css/autoplay */ \"(app-pages-browser)/./node_modules/swiper/modules/autoplay.css\");\n/* harmony import */ var _components_ui_TrustIndicators__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/TrustIndicators */ \"(app-pages-browser)/./components/ui/TrustIndicators.tsx\");\n/* harmony import */ var _components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ClientOnly */ \"(app-pages-browser)/./components/ClientOnly.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_ScrollAnimationWrapper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/ScrollAnimationWrapper */ \"(app-pages-browser)/./components/ui/ScrollAnimationWrapper.tsx\");\n/* harmony import */ var _components_ui_ParticleBackground__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/ParticleBackground */ \"(app-pages-browser)/./components/ui/ParticleBackground.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// Lazy load components outside of the component function\nconst HeroCarousel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_hero-carousel_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c = HeroCarousel;\nconst ProductSection = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_ProductSection_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c1 = ProductSection;\nconst CategoryTabs = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_CategoryTabs_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c2 = CategoryTabs;\nconst ProductCategories = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_ProductCategories_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c3 = ProductCategories;\nconst Homepage = ()=>{\n    _s();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    // Use a single API instance for all API calls\n    const { data, read } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL || '');\n    // Create state variables to store different data types\n    const [futureProduct, setFutureProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [futureProductLoading, setFutureProductLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [popularProduct, setPopularProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [popularProductLoading, setPopularProductLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categoriesLoading, setCategoriesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Use refs to track if data has been fetched\n    const initialDataFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [categoryProducts, setCategoryProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Track if category products have been fetched\n    const categoryProductsFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Function to fetch products for each category - optimized to prevent continuous API calls\n    const fetchCategoryProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Homepage.useCallback[fetchCategoryProducts]\": async ()=>{\n            // Skip if categories aren't loaded yet\n            if (!Array.isArray(categories) || categories.length === 0) return;\n            // Skip if we've already fetched and have data\n            if (categoryProductsFetchedRef.current && categoryProducts.length > 0 && categoryProducts.every({\n                \"Homepage.useCallback[fetchCategoryProducts]\": (cat)=>!cat.loading\n            }[\"Homepage.useCallback[fetchCategoryProducts]\"])) {\n                return;\n            }\n            // Limit to first 6 categories to avoid overwhelming the page and reduce API calls\n            const limitedCategories = categories.slice(0, 6);\n            // Set initial loading state\n            if (categoryProducts.length === 0) {\n                const initialCategoryProducts = limitedCategories.map({\n                    \"Homepage.useCallback[fetchCategoryProducts].initialCategoryProducts\": (category)=>({\n                            category,\n                            products: [],\n                            loading: true\n                        })\n                }[\"Homepage.useCallback[fetchCategoryProducts].initialCategoryProducts\"]);\n                setCategoryProducts(initialCategoryProducts);\n            }\n            // Fetch products for each category\n            const promises = limitedCategories.map({\n                \"Homepage.useCallback[fetchCategoryProducts].promises\": async (category, index)=>{\n                    try {\n                        var _result_results;\n                        // Use the single read function from our consolidated API instance\n                        const result = await read(\"\".concat((0,_constant_urls__WEBPACK_IMPORTED_MODULE_3__.CATEGORIZE_PRODUCTS)(category.slug), \"?page_size=8\"));\n                        return {\n                            index,\n                            category,\n                            products: (result === null || result === void 0 ? void 0 : (_result_results = result.results) === null || _result_results === void 0 ? void 0 : _result_results.products) || [],\n                            success: true\n                        };\n                    } catch (error) {\n                        console.error(\"Error fetching products for \".concat(category.name, \":\"), error);\n                        return {\n                            index,\n                            category,\n                            products: [],\n                            success: false\n                        };\n                    }\n                }\n            }[\"Homepage.useCallback[fetchCategoryProducts].promises\"]);\n            // Wait for all promises to resolve\n            const results = await Promise.all(promises);\n            // Update state once with all results\n            setCategoryProducts({\n                \"Homepage.useCallback[fetchCategoryProducts]\": (prev)=>{\n                    // Start with previous state or empty array\n                    const newState = prev.length > 0 ? [\n                        ...prev\n                    ] : limitedCategories.map({\n                        \"Homepage.useCallback[fetchCategoryProducts]\": (cat)=>({\n                                category: cat,\n                                products: [],\n                                loading: true\n                            })\n                    }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n                    // Update with new results\n                    results.forEach({\n                        \"Homepage.useCallback[fetchCategoryProducts]\": (result)=>{\n                            if (newState[result.index]) {\n                                newState[result.index] = {\n                                    ...newState[result.index],\n                                    products: result.products,\n                                    loading: false\n                                };\n                            }\n                        }\n                    }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n                    // Mark as fetched\n                    categoryProductsFetchedRef.current = true;\n                    return newState;\n                }\n            }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n        }\n    }[\"Homepage.useCallback[fetchCategoryProducts]\"], [\n        categories,\n        read\n    ]);\n    // Load all initial data with a single useEffect to reduce renders and prevent continuous API calls\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            // Skip if we've already loaded the data\n            if (initialDataFetchedRef.current) return;\n            // Create a flag to track if the component is still mounted\n            let isMounted = true;\n            const loadInitialData = {\n                \"Homepage.useEffect.loadInitialData\": async ()=>{\n                    try {\n                        setCategoriesLoading(true);\n                        setFutureProductLoading(true);\n                        setPopularProductLoading(true);\n                        // Load categories first\n                        const categoriesResult = await read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.CATEGORIES);\n                        // Only continue if component is still mounted\n                        if (!isMounted) return;\n                        // Update categories state\n                        if (categoriesResult) {\n                            setCategories(categoriesResult);\n                        }\n                        setCategoriesLoading(false);\n                        // Load featured and popular products in parallel\n                        const [featuredResult, popularResult] = await Promise.all([\n                            read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.FUTURED_PRODUCTS),\n                            read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.PRODUCTS + '?page_size=10') // Fetch 10 products for the Discover section\n                        ]);\n                        if (!isMounted) return;\n                        // Update featured products state\n                        if (featuredResult) {\n                            setFutureProduct(featuredResult);\n                        }\n                        // Update popular products state\n                        if (popularResult) {\n                            setPopularProduct(popularResult);\n                        }\n                        // Mark as fetched to prevent duplicate API calls\n                        initialDataFetchedRef.current = true;\n                        setFutureProductLoading(false);\n                        setPopularProductLoading(false);\n                    } catch (error) {\n                        console.error(\"Error loading initial data:\", error);\n                        setCategoriesLoading(false);\n                        setFutureProductLoading(false);\n                        setPopularProductLoading(false);\n                    }\n                }\n            }[\"Homepage.useEffect.loadInitialData\"];\n            loadInitialData();\n            // Cleanup function to prevent state updates after unmount\n            return ({\n                \"Homepage.useEffect\": ()=>{\n                    isMounted = false;\n                }\n            })[\"Homepage.useEffect\"];\n        }\n    }[\"Homepage.useEffect\"], [\n        read\n    ]);\n    // Fetch products for each category when categories are loaded\n    // This useEffect now depends only on categories and fetchCategoryProducts\n    // It will only run when categories change, not on every render\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            if (categories && categories.length > 0 && !categoryProductsFetchedRef.current) {\n                fetchCategoryProducts();\n            }\n        }\n    }[\"Homepage.useEffect\"], [\n        fetchCategoryProducts,\n        categories\n    ]);\n    // We no longer need featuredProductsForHero since we're using HeroCarousel\n    // Get featured products for product section - memoized to prevent recalculations\n    const featuredProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Homepage.useMemo[featuredProducts]\": ()=>{\n            if (futureProduct && Array.isArray(futureProduct)) {\n                return futureProduct;\n            } else if (futureProduct && futureProduct.results && Array.isArray(futureProduct.results)) {\n                return futureProduct.results;\n            } else if (futureProduct && futureProduct.products && Array.isArray(futureProduct.products)) {\n                return futureProduct.products;\n            }\n            // Return empty array if no products are available - no more static fallback\n            return [];\n        }\n    }[\"Homepage.useMemo[featuredProducts]\"], [\n        futureProduct\n    ]);\n    // Get popular products - memoized to prevent recalculations\n    const popularProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Homepage.useMemo[popularProducts]\": ()=>{\n            // First check our dedicated popularProduct state\n            if (popularProduct && Array.isArray(popularProduct)) {\n                return popularProduct;\n            } else if (popularProduct && popularProduct.results && Array.isArray(popularProduct.results)) {\n                return popularProduct.results;\n            } else if (popularProduct && popularProduct.products && Array.isArray(popularProduct.products)) {\n                return popularProduct.products;\n            }\n            // Fallback to data from the main API call if popularProduct isn't available\n            if (data && Array.isArray(data)) {\n                return data;\n            } else if (data && data.results && Array.isArray(data.results)) {\n                return data.results;\n            } else if (data && data.products && Array.isArray(data.products)) {\n                return data.products;\n            }\n            // Return empty array if no products are available - no more static fallback\n            return [];\n        }\n    }[\"Homepage.useMemo[popularProducts]\"], [\n        popularProduct,\n        data\n    ]);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_11__.useScroll)();\n    const backgroundY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        \"0%\",\n        \"50%\"\n    ]);\n    const textY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        \"0%\",\n        \"200%\"\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                className: \"fixed inset-0 w-full h-full\",\n                style: {\n                    y: backgroundY\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ParticleBackground__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        className: \"absolute inset-0 bg-gradient-to-tr from-blue-900/20 via-transparent to-purple-900/20\",\n                        animate: {\n                            background: [\n                                \"linear-gradient(45deg, rgba(30, 58, 138, 0.2) 0%, transparent 50%, rgba(88, 28, 135, 0.2) 100%)\",\n                                \"linear-gradient(45deg, rgba(88, 28, 135, 0.2) 0%, transparent 50%, rgba(30, 58, 138, 0.2) 100%)\",\n                                \"linear-gradient(45deg, rgba(30, 58, 138, 0.2) 0%, transparent 50%, rgba(88, 28, 135, 0.2) 100%)\"\n                            ]\n                        },\n                        transition: {\n                            duration: 8,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        className: \"absolute top-[10%] right-[10%] w-[300px] h-[300px] rounded-full bg-gradient-to-br from-blue-500/30 to-purple-500/30 blur-3xl\",\n                        animate: {\n                            x: [\n                                0,\n                                50,\n                                0\n                            ],\n                            y: [\n                                0,\n                                -30,\n                                0\n                            ],\n                            scale: [\n                                1,\n                                1.1,\n                                1\n                            ]\n                        },\n                        transition: {\n                            duration: 6,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        className: \"absolute top-[60%] left-[5%] w-[400px] h-[400px] rounded-full bg-gradient-to-tr from-purple-500/20 to-pink-500/20 blur-3xl\",\n                        animate: {\n                            x: [\n                                0,\n                                -40,\n                                0\n                            ],\n                            y: [\n                                0,\n                                40,\n                                0\n                            ],\n                            scale: [\n                                1,\n                                0.9,\n                                1\n                            ]\n                        },\n                        transition: {\n                            duration: 8,\n                            repeat: Infinity,\n                            ease: \"easeInOut\",\n                            delay: 1\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        className: \"absolute bottom-[10%] right-[20%] w-[250px] h-[250px] rounded-full bg-gradient-to-bl from-indigo-500/25 to-blue-500/25 blur-3xl\",\n                        animate: {\n                            x: [\n                                0,\n                                30,\n                                0\n                            ],\n                            y: [\n                                0,\n                                -20,\n                                0\n                            ],\n                            scale: [\n                                1,\n                                1.2,\n                                1\n                            ]\n                        },\n                        transition: {\n                            duration: 7,\n                            repeat: Infinity,\n                            ease: \"easeInOut\",\n                            delay: 2\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-40\",\n                        style: {\n                            backgroundImage: \"url(\\\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\\\")\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen flex flex-col w-full relative z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                            className: \"relative z-20\",\n                            initial: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCategories, {\n                                categories: categories !== null && categories !== void 0 ? categories : [],\n                                variant: \"navigation\",\n                                showTitle: false,\n                                showViewAll: false,\n                                maxCategories: 12,\n                                accentColor: \"primary\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Suspense), {\n                                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[400px] bg-gray-100/10 animate-pulse rounded-xl backdrop-blur-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[300px] bg-gray-100/10 animate-pulse rounded-xl backdrop-blur-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[300px] bg-gray-100/10 animate-pulse rounded-xl backdrop-blur-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 15\n                                }, void 0),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ScrollAnimationWrapper__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        animationType: \"scale\",\n                                        duration: 0.8,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                            className: \"relative w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroCarousel, {}, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 mt-6 mb-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_TrustIndicators__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ScrollAnimationWrapper__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        animationType: \"fadeUp\",\n                                        delay: 0.2,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                            title: \"Featured Products\",\n                                            subtitle: \"Discover our handpicked selection of premium products\",\n                                            products: featuredProducts,\n                                            loading: futureProductLoading,\n                                            viewAllLink: \"/shop\",\n                                            accentColor: \"primary\",\n                                            columns: {\n                                                xs: 2,\n                                                sm: 2,\n                                                md: 3,\n                                                lg: 4,\n                                                xl: 4,\n                                                \"2xl\": 5\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ScrollAnimationWrapper__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        animationType: \"fadeLeft\",\n                                        delay: 0.3,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                            title: \"Discover Products\",\n                                            subtitle: \"Explore our most popular items\",\n                                            products: popularProducts,\n                                            loading: popularProductLoading,\n                                            viewAllLink: \"/shop\",\n                                            accentColor: \"secondary\",\n                                            columns: {\n                                                xs: 2,\n                                                sm: 2,\n                                                md: 3,\n                                                lg: 4,\n                                                xl: 4,\n                                                \"2xl\": 5\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ScrollAnimationWrapper__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        animationType: \"fadeRight\",\n                                        delay: 0.4,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCategories, {\n                                            title: \"Shop by Category\",\n                                            subtitle: \"Browse our collection by category\",\n                                            categories: categories || [],\n                                            accentColor: \"tertiary\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    categoryProducts.some((cat)=>cat.products && cat.products.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ScrollAnimationWrapper__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        animationType: \"scale\",\n                                        delay: 0.5,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CategoryTabs, {\n                                            title: \"Browse Products by Category\",\n                                            subtitle: \"Filter products by your favorite categories\",\n                                            categories: categories || [],\n                                            categoryProducts: categoryProducts.filter((cat)=>cat.products && cat.products.length > 0),\n                                            accentColor: \"primary\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    categoryProducts.map((categoryData, categoryIndex)=>// Only show categories with at least 4 products\n                                        Array.isArray(categoryData.products) && categoryData.products.length >= 4 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ScrollAnimationWrapper__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            animationType: categoryIndex % 2 === 0 ? \"fadeLeft\" : \"fadeRight\",\n                                            delay: 0.1 * categoryIndex,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                                title: categoryData.category.name,\n                                                products: categoryData.products,\n                                                loading: categoryData.loading,\n                                                viewAllLink: \"/shop?category=\".concat(categoryData.category.slug),\n                                                accentColor: categoryIndex % 3 === 0 ? \"primary\" : categoryIndex % 3 === 1 ? \"secondary\" : \"tertiary\",\n                                                columns: {\n                                                    xs: 2,\n                                                    sm: 2,\n                                                    md: 3,\n                                                    lg: 4,\n                                                    xl: 4,\n                                                    \"2xl\": 5\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, categoryData.category.id, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 17\n                                        }, undefined) : null)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                    lineNumber: 335,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 334,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n        lineNumber: 268,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Homepage, \"crKxmcPE2IAbLsfV5maM98rMu58=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast,\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        framer_motion__WEBPACK_IMPORTED_MODULE_11__.useScroll,\n        framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_12__.useTransform\n    ];\n});\n_c4 = Homepage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Homepage);\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"HeroCarousel\");\n$RefreshReg$(_c1, \"ProductSection\");\n$RefreshReg$(_c2, \"CategoryTabs\");\n$RefreshReg$(_c3, \"ProductCategories\");\n$RefreshReg$(_c4, \"Homepage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});