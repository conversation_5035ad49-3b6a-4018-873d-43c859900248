"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[slug]/page",{

/***/ "(app-pages-browser)/./components/product/ProductCard.tsx":
/*!********************************************!*\
  !*** ./components/product/ProductCard.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Eye_Heart_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Heart,ShoppingCart!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Heart_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Heart,ShoppingCart!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Heart_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Heart,ShoppingCart!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../hooks/useApi */ \"(app-pages-browser)/./hooks/useApi.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _hooks_useStorage__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../hooks/useStorage */ \"(app-pages-browser)/./hooks/useStorage.ts\");\n/* harmony import */ var _utils_imageUtils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../utils/imageUtils */ \"(app-pages-browser)/./utils/imageUtils.ts\");\n/* harmony import */ var _utils_imageProcessing__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../utils/imageProcessing */ \"(app-pages-browser)/./utils/imageProcessing.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ProductCard = (props)=>{\n    _s();\n    const { image, name, slug, price, rating, id, category, brand, removeWhiteBackground: enableWhiteBackgroundRemoval = true } = props;\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const { create, loading } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_7__.MAIN_URL || '');\n    const { create: createWishlist } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_7__.MAIN_URL || '');\n    const { remove } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_7__.MAIN_URL || '');\n    const { status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_8__.useSession)();\n    const pathName = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname)();\n    const [wishlistIds, setWishlistIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [processedImageUrl, setProcessedImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isAddingToCart, setIsAddingToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const storage = (0,_hooks_useStorage__WEBPACK_IMPORTED_MODULE_9__[\"default\"])('local');\n    // Check if product is in wishlist\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductCard.useEffect\": ()=>{\n            const storedWishlistIds = storage.getItem(\"wishlistIds\");\n            setWishlistIds(storedWishlistIds);\n        }\n    }[\"ProductCard.useEffect\"], [\n        storage\n    ]);\n    const isAdded = wishlistIds ? wishlistIds.includes(id.toString()) : false;\n    // Handle add to cart\n    const handleAddToCart = async (e)=>{\n        e.stopPropagation();\n        if (status === \"unauthenticated\") {\n            router.push(\"/auth/login?callbackUrl=%2F\".concat(pathName));\n            return;\n        }\n        setIsAddingToCart(true);\n        try {\n            await create(_constant_urls__WEBPACK_IMPORTED_MODULE_7__.ADD_TO_CART, {\n                product_id: id,\n                quantity: 1\n            });\n            toast({\n                variant: \"success\",\n                title: \"Added to Cart\",\n                description: \"\".concat(name, \" has been added to your cart\")\n            });\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to add product to cart\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsAddingToCart(false);\n        }\n    };\n    // Handle quick view\n    const handleQuickView = (e)=>{\n        e.stopPropagation();\n        router.push(\"/product/\".concat(slug));\n    };\n    // Handle add to wishlist\n    const handleAddToWishlist = async (e)=>{\n        e.stopPropagation();\n        if (status === \"unauthenticated\") {\n            router.push(\"/auth/login?callbackUrl=%2F\".concat(pathName));\n            return;\n        }\n        try {\n            await createWishlist(_constant_urls__WEBPACK_IMPORTED_MODULE_7__.ADD_TO_WISHLIST, {\n                product_id: id\n            });\n            // Update local storage\n            const currentIds = storage.getItem(\"wishlistIds\") || \"\";\n            const newIds = currentIds ? \"\".concat(currentIds, \",\").concat(id) : \"\".concat(id);\n            storage.setItem(\"wishlistIds\", newIds);\n            setWishlistIds(newIds);\n            toast({\n                variant: \"success\",\n                title: \"Added to Wishlist\",\n                description: \"\".concat(name, \" has been added to your wishlist\")\n            });\n        } catch (error) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"Failed to add product to wishlist\"\n            });\n        }\n    };\n    // Handle remove from wishlist\n    const handleRemoveFromWishlist = async (e)=>{\n        e.stopPropagation();\n        try {\n            await remove(\"\".concat(_constant_urls__WEBPACK_IMPORTED_MODULE_7__.REMOVE_FROM_WISHLIST).concat(id, \"/\"));\n            // Update local storage\n            const currentIds = storage.getItem(\"wishlistIds\") || \"\";\n            const idsArray = currentIds.split(\",\");\n            const filteredIds = idsArray.filter((item)=>item !== id.toString()).join(\",\");\n            storage.setItem(\"wishlistIds\", filteredIds);\n            setWishlistIds(filteredIds);\n            toast({\n                variant: \"info\",\n                title: \"Removed from Wishlist\",\n                description: \"\".concat(name, \" has been removed from your wishlist\")\n            });\n        } catch (error) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"Failed to remove product from wishlist\"\n            });\n        }\n    };\n    // Navigate to product page when card is clicked\n    const navigateToProduct = ()=>{\n        router.push(\"/product/\".concat(slug));\n    };\n    // Configuration for white background removal\n    const backgroundRemovalConfig = {\n        whiteThreshold: 245,\n        tolerance: 20,\n        edgePreservation: true,\n        smoothing: true,\n        quality: 1.0\n    };\n    // Process image when component mounts or image changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductCard.useEffect\": ()=>{\n            const processImage = {\n                \"ProductCard.useEffect.processImage\": async ()=>{\n                    if (image && enableWhiteBackgroundRemoval) {\n                        try {\n                            const imageUrl = (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_10__.getImageUrl)(image);\n                            const processed = await (0,_utils_imageProcessing__WEBPACK_IMPORTED_MODULE_11__.removeWhiteBackground)(imageUrl, backgroundRemovalConfig);\n                            setProcessedImageUrl(processed);\n                        } catch (error) {\n                            console.warn('Failed to process image:', error);\n                            // Fallback to original image\n                            setProcessedImageUrl(null);\n                        }\n                    } else {\n                        setProcessedImageUrl(null);\n                    }\n                }\n            }[\"ProductCard.useEffect.processImage\"];\n            processImage();\n        }\n    }[\"ProductCard.useEffect\"], [\n        image,\n        enableWhiteBackgroundRemoval,\n        backgroundRemovalConfig\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"overflow-hidden h-full rounded-lg xs:rounded-xl flex flex-col relative glass-product-card cursor-pointer\",\n        onClick: navigateToProduct,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"p-0 h-full flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative overflow-hidden bg-white/5 backdrop-blur-sm\",\n                        style: {\n                            paddingBottom: \"100%\"\n                        },\n                        children: [\n                            brand && typeof brand !== 'string' && ((brand === null || brand === void 0 ? void 0 : brand.image_url) || (brand === null || brand === void 0 ? void 0 : brand.image)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 left-2 z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 xs:w-10 xs:h-10 overflow-hidden rounded-md border border-white/20 shadow-md bg-white/10 backdrop-blur-sm flex items-center justify-center p-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: (brand === null || brand === void 0 ? void 0 : brand.image_url) || \"\".concat(\"http://localhost:8000\" || 0).concat(brand === null || brand === void 0 ? void 0 : brand.image),\n                                        alt: \"\".concat(brand === null || brand === void 0 ? void 0 : brand.name, \" logo\"),\n                                        className: \"max-w-full max-h-full object-contain\",\n                                        onError: (e)=>{\n                                            // Hide the image on error\n                                            const imgElement = e.currentTarget;\n                                            imgElement.style.display = 'none';\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductCard.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductCard.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, undefined),\n                            !imageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6 h-6 sm:w-8 sm:h-8 border-3 sm:border-4 border-theme-accent-primary/30 border-t-theme-accent-primary rounded-full animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductCard.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 flex items-center justify-center p-2 xs:p-3 sm:p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: processedImageUrl || (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_10__.getImageUrl)(image, '/assets/products/product-placeholder.svg'),\n                                    alt: name,\n                                    className: \"max-w-full max-h-full object-contain transition-all duration-300 \".concat(imageLoaded ? 'opacity-100' : 'opacity-0'),\n                                    onLoad: ()=>setImageLoaded(true),\n                                    onError: (e)=>{\n                                        const imgElement = e.target;\n                                        // If processed image fails, try original image\n                                        if (processedImageUrl && imgElement.src === processedImageUrl) {\n                                            imgElement.src = (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_10__.getImageUrl)(image, '/assets/products/product-placeholder.svg');\n                                            return;\n                                        }\n                                        // Try to load a category-specific image first\n                                        if (!imgElement.src.includes('/assets/products/')) {\n                                            // Extract category name if available\n                                            const categoryName = typeof category === 'object' && (category === null || category === void 0 ? void 0 : category.name) ? category.name.toLowerCase().replace(/\\s+/g, '-') : 'product';\n                                            // Try to load a category-specific placeholder\n                                            imgElement.src = \"/assets/products/\".concat(categoryName, \".svg\");\n                                            // Add a second error handler for the category placeholder\n                                            imgElement.onerror = ()=>{\n                                                // If category placeholder fails, use generic product placeholder\n                                                imgElement.src = '/assets/products/product-placeholder.svg';\n                                                imgElement.onerror = null; // Prevent infinite error loop\n                                            };\n                                        }\n                                        setImageLoaded(true);\n                                    },\n                                    style: {\n                                        maxHeight: \"85%\",\n                                        maxWidth: \"85%\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductCard.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductCard.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductCard.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-2 xs:p-3 sm:p-4 flex-grow flex flex-col justify-between bg-white/5 backdrop-blur-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-medium text-white line-clamp-2 text-left min-h-[2.5rem] text-xs xs:text-sm sm:text-base mb-1 xs:mb-2\",\n                                children: name\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductCard.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2 xs:mb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-bold text-blue-400 text-sm xs:text-base sm:text-lg\",\n                                            children: [\n                                                \"₹\",\n                                                price\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductCard.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductCard.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col xs:flex-row items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                className: \"w-full xs:flex-1 bg-theme-accent-primary text-white hover:bg-theme-accent-hover border-none h-9\",\n                                                onClick: handleAddToCart,\n                                                disabled: isAddingToCart || loading,\n                                                children: [\n                                                    isAddingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductCard.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 19\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Heart_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductCard.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs sm:text-sm whitespace-nowrap\",\n                                                        children: \"Add to Cart\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductCard.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductCard.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center gap-2 w-full xs:w-auto mt-2 xs:mt-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        className: \"bg-white/10 border-white/20 hover:bg-white/20 text-white h-9 w-9 backdrop-blur-sm\",\n                                                        onClick: isAdded ? handleRemoveFromWishlist : handleAddToWishlist,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Heart_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4\",\n                                                            fill: isAdded ? \"rgb(236 72 153)\" : \"none\",\n                                                            stroke: isAdded ? \"rgb(236 72 153)\" : \"currentColor\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductCard.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductCard.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        className: \"bg-white/10 border-white/20 hover:bg-white/20 text-white h-9 w-9 backdrop-blur-sm\",\n                                                        onClick: handleQuickView,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Heart_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductCard.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductCard.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductCard.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductCard.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductCard.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductCard.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductCard.tsx\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                style: {\n                    display: 'none'\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductCard.tsx\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\ProductCard.tsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProductCard, \"nEJHh8dfB/RsQ24cU0cB5CNJYmg=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter,\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        next_auth_react__WEBPACK_IMPORTED_MODULE_8__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname,\n        _hooks_useStorage__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    ];\n});\n_c = ProductCard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductCard);\nvar _c;\n$RefreshReg$(_c, \"ProductCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/product/ProductCard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./utils/imageProcessing.ts":
/*!**********************************!*\
  !*** ./utils/imageProcessing.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_CONFIG: () => (/* binding */ DEFAULT_CONFIG),\n/* harmony export */   hasWhiteBackground: () => (/* binding */ hasWhiteBackground),\n/* harmony export */   previewWhiteBackgroundRemoval: () => (/* binding */ previewWhiteBackgroundRemoval),\n/* harmony export */   removeWhiteBackground: () => (/* binding */ removeWhiteBackground)\n/* harmony export */ });\n/**\n * Image Processing Utilities\n * \n * This module provides utilities for processing product images,\n * particularly for removing white backgrounds dynamically in the browser.\n */ const DEFAULT_CONFIG = {\n    whiteThreshold: 245,\n    tolerance: 20,\n    edgePreservation: true,\n    smoothing: true,\n    quality: 1.0\n};\n/**\n * Removes white background from an image using Canvas API\n * \n * @param imageUrl - URL of the image to process\n * @param config - Configuration options for the removal process\n * @returns Promise that resolves to a data URL of the processed image\n */ function removeWhiteBackground(imageUrl) {\n    let config = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const finalConfig = {\n        ...DEFAULT_CONFIG,\n        ...config\n    };\n    return new Promise((resolve, reject)=>{\n        const img = new Image();\n        img.crossOrigin = 'anonymous';\n        img.onload = ()=>{\n            try {\n                const canvas = document.createElement('canvas');\n                const ctx = canvas.getContext('2d');\n                if (!ctx) {\n                    reject(new Error('Canvas context not available'));\n                    return;\n                }\n                canvas.width = img.width;\n                canvas.height = img.height;\n                // Draw the image\n                ctx.drawImage(img, 0, 0);\n                // Get image data\n                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n                const data = imageData.data;\n                // First pass: identify white pixels\n                const whitePixels = new Set();\n                for(let i = 0; i < data.length; i += 4){\n                    const r = data[i];\n                    const g = data[i + 1];\n                    const b = data[i + 2];\n                    // Check if pixel is close to white\n                    if (r > finalConfig.whiteThreshold - finalConfig.tolerance && g > finalConfig.whiteThreshold - finalConfig.tolerance && b > finalConfig.whiteThreshold - finalConfig.tolerance) {\n                        whitePixels.add(i);\n                    }\n                }\n                // Second pass: apply transparency with optional edge preservation\n                for(let i = 0; i < data.length; i += 4){\n                    if (whitePixels.has(i)) {\n                        if (finalConfig.edgePreservation) {\n                            // Check if this pixel is on an edge (has non-white neighbors)\n                            const x = i / 4 % canvas.width;\n                            const y = Math.floor(i / 4 / canvas.width);\n                            let isEdge = false;\n                            // Check 3x3 neighborhood\n                            for(let dy = -1; dy <= 1; dy++){\n                                for(let dx = -1; dx <= 1; dx++){\n                                    const nx = x + dx;\n                                    const ny = y + dy;\n                                    if (nx >= 0 && nx < canvas.width && ny >= 0 && ny < canvas.height) {\n                                        const neighborIndex = (ny * canvas.width + nx) * 4;\n                                        if (!whitePixels.has(neighborIndex)) {\n                                            isEdge = true;\n                                            break;\n                                        }\n                                    }\n                                }\n                                if (isEdge) break;\n                            }\n                            if (isEdge && finalConfig.smoothing) {\n                                // Apply partial transparency for smooth edges\n                                data[i + 3] = Math.floor(data[i + 3] * 0.3);\n                            } else if (!isEdge) {\n                                // Full transparency for interior white pixels\n                                data[i + 3] = 0;\n                            }\n                        } else {\n                            // Simple transparency without edge preservation\n                            data[i + 3] = 0;\n                        }\n                    }\n                }\n                // Put the modified image data back\n                ctx.putImageData(imageData, 0, 0);\n                // Convert to data URL with specified quality\n                const processedDataUrl = canvas.toDataURL('image/png', finalConfig.quality);\n                resolve(processedDataUrl);\n            } catch (error) {\n                reject(error);\n            }\n        };\n        img.onerror = ()=>{\n            reject(new Error('Failed to load image'));\n        };\n        img.src = imageUrl;\n    });\n}\n/**\n * Checks if an image likely has a white background\n * \n * @param imageUrl - URL of the image to analyze\n * @returns Promise that resolves to true if the image likely has a white background\n */ function hasWhiteBackground(imageUrl) {\n    return new Promise((resolve, reject)=>{\n        const img = new Image();\n        img.crossOrigin = 'anonymous';\n        img.onload = ()=>{\n            try {\n                const canvas = document.createElement('canvas');\n                const ctx = canvas.getContext('2d');\n                if (!ctx) {\n                    reject(new Error('Canvas context not available'));\n                    return;\n                }\n                canvas.width = img.width;\n                canvas.height = img.height;\n                ctx.drawImage(img, 0, 0);\n                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n                const data = imageData.data;\n                let whitePixelCount = 0;\n                const totalPixels = data.length / 4;\n                const whiteThreshold = 240;\n                const tolerance = 15;\n                for(let i = 0; i < data.length; i += 4){\n                    const r = data[i];\n                    const g = data[i + 1];\n                    const b = data[i + 2];\n                    if (r > whiteThreshold - tolerance && g > whiteThreshold - tolerance && b > whiteThreshold - tolerance) {\n                        whitePixelCount++;\n                    }\n                }\n                // Consider it has white background if more than 30% of pixels are white\n                const whitePercentage = whitePixelCount / totalPixels;\n                resolve(whitePercentage > 0.3);\n            } catch (error) {\n                reject(error);\n            }\n        };\n        img.onerror = ()=>{\n            reject(new Error('Failed to load image'));\n        };\n        img.src = imageUrl;\n    });\n}\n/**\n * Creates a preview of the white background removal effect\n * \n * @param imageUrl - URL of the image to preview\n * @param canvasElement - Canvas element to render the preview\n * @param config - Configuration options\n */ function previewWhiteBackgroundRemoval(imageUrl, canvasElement) {\n    let config = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n    return new Promise((resolve, reject)=>{\n        removeWhiteBackground(imageUrl, config).then((processedDataUrl)=>{\n            const img = new Image();\n            img.onload = ()=>{\n                const ctx = canvasElement.getContext('2d');\n                if (!ctx) {\n                    reject(new Error('Canvas context not available'));\n                    return;\n                }\n                canvasElement.width = img.width;\n                canvasElement.height = img.height;\n                ctx.drawImage(img, 0, 0);\n                resolve();\n            };\n            img.onerror = ()=>reject(new Error('Failed to load processed image'));\n            img.src = processedDataUrl;\n        }).catch(reject);\n    });\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./utils/imageProcessing.ts\n"));

/***/ })

});