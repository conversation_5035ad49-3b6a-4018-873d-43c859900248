"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_components_home_hero-carousel_tsx",{

/***/ "(app-pages-browser)/./components/home/<USER>":
/*!*************************************************!*\
  !*** ./components/home/<USER>
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AmazonStyleCarousel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction AmazonStyleCarousel(param) {\n    let { slides, autoplayInterval = 5000, className } = param;\n    _s();\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAutoPlaying, setIsAutoPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const autoPlayRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Reset autoplay timer when slide changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AmazonStyleCarousel.useEffect\": ()=>{\n            if (isAutoPlaying) {\n                if (autoPlayRef.current) {\n                    clearInterval(autoPlayRef.current);\n                }\n                autoPlayRef.current = setInterval({\n                    \"AmazonStyleCarousel.useEffect\": ()=>{\n                        setCurrentSlide({\n                            \"AmazonStyleCarousel.useEffect\": (prev)=>(prev + 1) % slides.length\n                        }[\"AmazonStyleCarousel.useEffect\"]);\n                    }\n                }[\"AmazonStyleCarousel.useEffect\"], autoplayInterval);\n            }\n            return ({\n                \"AmazonStyleCarousel.useEffect\": ()=>{\n                    if (autoPlayRef.current) {\n                        clearInterval(autoPlayRef.current);\n                    }\n                }\n            })[\"AmazonStyleCarousel.useEffect\"];\n        }\n    }[\"AmazonStyleCarousel.useEffect\"], [\n        currentSlide,\n        isAutoPlaying,\n        autoplayInterval,\n        slides.length\n    ]);\n    // Pause autoplay on hover\n    const pauseAutoPlay = ()=>setIsAutoPlaying(false);\n    const resumeAutoPlay = ()=>setIsAutoPlaying(true);\n    const goToSlide = (index)=>{\n        setCurrentSlide(index);\n    };\n    const goToPrevSlide = ()=>{\n        setCurrentSlide((prev)=>prev === 0 ? slides.length - 1 : prev - 1);\n    };\n    const goToNextSlide = ()=>{\n        setCurrentSlide((prev)=>(prev + 1) % slides.length);\n    };\n    // Handle touch events for mobile swipe\n    const handleTouchStart = (e)=>{\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const distance = touchStart - touchEnd;\n        const isLeftSwipe = distance > 50;\n        const isRightSwipe = distance < -50;\n        if (isLeftSwipe) {\n            goToNextSlide();\n        } else if (isRightSwipe) {\n            goToPrevSlide();\n        }\n        setTouchStart(null);\n        setTouchEnd(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"relative overflow-hidden rounded-xl shadow-lg mt-2 bg-transparent from-theme-header to-theme-header/90\", className),\n        onMouseEnter: pauseAutoPlay,\n        onMouseLeave: resumeAutoPlay,\n        ref: containerRef,\n        onTouchStart: handleTouchStart,\n        onTouchMove: handleTouchMove,\n        onTouchEnd: handleTouchEnd,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-opacity-10 z-0 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 opacity-10 bg-[radial-gradient(#ffffff33_1px,transparent_1px)] bg-[size:20px_20px]\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex transition-transform duration-700 ease-out h-[280px] xs:h-[320px] sm:h-[380px] md:h-[420px] lg:h-[480px] xl:h-[520px]\",\n                style: {\n                    transform: \"translateX(-\".concat(currentSlide * 100, \"%)\")\n                },\n                children: slides.map((slide, index)=>{\n                    var _slide_brand, _slide_brand1, _slide_brand2, _slide_brand3, _slide_brand4;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-w-full relative group cursor-pointer\",\n                        onClick: ()=>{\n                            try {\n                                window.location.href = slide.link || \"/shop\";\n                            } catch (error) {\n                                console.error(\"Navigation error:\", error);\n                                window.location.href = \"/shop\";\n                            }\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-[#8a6f4d]/20 to-[#d9c3a9]/10\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 right-0 bottom-0 w-[45%] xs:w-[42%] sm:w-[45%] md:w-[50%] flex items-center justify-center p-2 xs:p-3 sm:p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-full h-full max-w-[95%] max-h-[85%] xs:max-h-[90%]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: slide.image || '/home/<USER>',\n                                        alt: slide.title || 'Product Image',\n                                        fill: true,\n                                        sizes: \"(max-width: 640px) 50vw, (max-width: 768px) 45vw, (max-width: 1024px) 50vw, 50vw\",\n                                        className: \"object-contain object-center\",\n                                        priority: index === 0,\n                                        onError: (e)=>{\n                                            // Fallback to a placeholder if image fails to load\n                                            const imgElement = e.currentTarget;\n                                            if (!imgElement.src.includes('placeholder') && !imgElement.src.includes('image-1.png')) {\n                                                imgElement.src = '/home/<USER>';\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-black/50 via-black/30 to-transparent\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-[url('/patterns/dot-pattern.svg')] bg-repeat opacity-10\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white p-2 xs:p-3 sm:p-4 md:p-6 lg:p-8 xl:p-12 w-[55%] xs:w-[58%] sm:w-[55%] md:w-[50%] lg:w-[45%] xl:w-[40%]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-1.5 xs:gap-2 sm:gap-3 mb-1 xs:mb-1.5 sm:mb-2 md:mb-3\",\n                                            children: [\n                                                slide.brand && typeof slide.brand !== 'string' && (((_slide_brand = slide.brand) === null || _slide_brand === void 0 ? void 0 : _slide_brand.image_url) || ((_slide_brand1 = slide.brand) === null || _slide_brand1 === void 0 ? void 0 : _slide_brand1.image)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 mt-0.5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-6 h-6 xs:w-8 xs:h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 lg:w-14 lg:h-14 overflow-hidden rounded-md border border-white/80 shadow-lg bg-white flex items-center justify-center p-0.5\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: ((_slide_brand2 = slide.brand) === null || _slide_brand2 === void 0 ? void 0 : _slide_brand2.image_url) || \"\".concat(\"http://localhost:8000\" || 0).concat((_slide_brand3 = slide.brand) === null || _slide_brand3 === void 0 ? void 0 : _slide_brand3.image),\n                                                            alt: \"\".concat((_slide_brand4 = slide.brand) === null || _slide_brand4 === void 0 ? void 0 : _slide_brand4.name, \" logo\"),\n                                                            className: \"max-w-full max-h-full object-contain\",\n                                                            onError: (e)=>{\n                                                                // Hide the image on error\n                                                                const imgElement = e.currentTarget;\n                                                                imgElement.style.display = 'none';\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-sm xs:text-base sm:text-lg md:text-xl lg:text-2xl xl:text-3xl 2xl:text-4xl font-bold   [text-shadow:_0_1px_3px_rgb(0_0_0_/_50%)] leading-tight flex-1 pr-1\",\n                                                    children: slide.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs xs:text-xs sm:text-sm md:text-base lg:text-lg mb-1.5 xs:mb-2 sm:mb-3 md:mb-4 text-white/90   [text-shadow:_0_1px_2px_rgb(0_0_0_/_30%)] line-clamp-2 leading-snug max-w-[95%] break-words\",\n                                            children: slide.subtitle\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, this),\n                                        slide.code && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"inline-block bg-white/20 backdrop-blur-sm px-2 xs:px-2.5 sm:px-3 py-0.5 xs:py-1 rounded-full   text-xs xs:text-xs sm:text-sm md:text-base font-medium mb-1.5 xs:mb-2 sm:mb-3   [text-shadow:_0_1px_1px_rgb(0_0_0_/_20%)] whitespace-nowrap\",\n                                            children: slide.code\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1.5 xs:mt-2 sm:mt-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                href: slide.link || \"/shop\",\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                // Additional error handling can be added here if needed\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    className: \"bg-[#2ECC71] hover:bg-[#27AE60] text-white font-medium   px-2.5 xs:px-3 sm:px-4 md:px-6 py-1 xs:py-1.5 sm:py-2 rounded-full   transition-all duration-300 hover:shadow-lg hover:scale-105   text-xs xs:text-xs sm:text-sm md:text-base whitespace-nowrap\",\n                                                    children: slide.cta || \"Shop Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-0 left-0 right-[40%] xs:right-[42%] sm:right-[45%] md:right-[50%] p-1.5 xs:p-2 sm:p-3 bg-gradient-to-t from-black/60 to-transparent\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white text-xs xs:text-xs sm:text-sm\",\n                                    children: slide.specs && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mb-0.5 [text-shadow:_0_1px_1px_rgb(0_0_0_/_30%)] leading-tight break-words max-w-full opacity-90\",\n                                        children: slide.specs\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: (e)=>{\n                    e.stopPropagation();\n                    goToPrevSlide();\n                },\n                className: \"absolute left-2 xs:left-4 top-1/2 -translate-y-1/2 w-8 h-8 xs:w-10 xs:h-10 items-center justify-center   bg-theme-accent-primary hover:bg-theme-accent-hover text-white rounded-full shadow-md z-10 transition-all duration-300   hover:shadow-lg hover:scale-110 hidden xs:inline-flex\",\n                \"aria-label\": \"Previous slide\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 xs:h-6 xs:w-6 text-white transition-transform duration-300 hover:-translate-x-0.5\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                lineNumber: 244,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: (e)=>{\n                    e.stopPropagation();\n                    goToNextSlide();\n                },\n                className: \"absolute right-2 xs:right-4 top-1/2 -translate-y-1/2 w-8 h-8 xs:w-10 xs:h-10 items-center justify-center   bg-theme-accent-primary hover:bg-theme-accent-hover text-white rounded-full shadow-md z-10 transition-all duration-300   hover:shadow-lg hover:scale-110 hidden xs:inline-flex\",\n                \"aria-label\": \"Next slide\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 xs:h-6 xs:w-6 text-white transition-transform duration-300 hover:translate-x-0.5\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-2 xs:bottom-4 left-1/2 -translate-x-1/2 flex gap-1 xs:gap-2 z-10\",\n                children: slides.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>goToSlide(index),\n                        className: \"w-2 h-2 xs:w-3 xs:h-3 rounded-full transition-all duration-300 \".concat(index === currentSlide ? \"bg-theme-accent-secondary scale-110\" : \"bg-white/50 hover:bg-white/70 hover:scale-105\"),\n                        \"aria-label\": \"Go to slide \".concat(index + 1)\n                    }, index, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n_s(AmazonStyleCarousel, \"q+8ilV0we8mV9YlTA9aGx5Xg1EA=\");\n_c = AmazonStyleCarousel;\nvar _c;\n$RefreshReg$(_c, \"AmazonStyleCarousel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/home/<USER>"));

/***/ })

});