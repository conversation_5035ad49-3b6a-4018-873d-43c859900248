"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_components_home_hero-carousel_tsx";
exports.ids = ["_ssr_components_home_hero-carousel_tsx"];
exports.modules = {

/***/ "(ssr)/./components/home/<USER>":
/*!*************************************************!*\
  !*** ./components/home/<USER>
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AmazonStyleCarousel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction AmazonStyleCarousel({ slides, autoplayInterval = 5000, className }) {\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAutoPlaying, setIsAutoPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const autoPlayRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Reset autoplay timer when slide changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AmazonStyleCarousel.useEffect\": ()=>{\n            if (isAutoPlaying) {\n                if (autoPlayRef.current) {\n                    clearInterval(autoPlayRef.current);\n                }\n                autoPlayRef.current = setInterval({\n                    \"AmazonStyleCarousel.useEffect\": ()=>{\n                        setCurrentSlide({\n                            \"AmazonStyleCarousel.useEffect\": (prev)=>(prev + 1) % slides.length\n                        }[\"AmazonStyleCarousel.useEffect\"]);\n                    }\n                }[\"AmazonStyleCarousel.useEffect\"], autoplayInterval);\n            }\n            return ({\n                \"AmazonStyleCarousel.useEffect\": ()=>{\n                    if (autoPlayRef.current) {\n                        clearInterval(autoPlayRef.current);\n                    }\n                }\n            })[\"AmazonStyleCarousel.useEffect\"];\n        }\n    }[\"AmazonStyleCarousel.useEffect\"], [\n        currentSlide,\n        isAutoPlaying,\n        autoplayInterval,\n        slides.length\n    ]);\n    // Pause autoplay on hover\n    const pauseAutoPlay = ()=>setIsAutoPlaying(false);\n    const resumeAutoPlay = ()=>setIsAutoPlaying(true);\n    const goToSlide = (index)=>{\n        setCurrentSlide(index);\n    };\n    const goToPrevSlide = ()=>{\n        setCurrentSlide((prev)=>prev === 0 ? slides.length - 1 : prev - 1);\n    };\n    const goToNextSlide = ()=>{\n        setCurrentSlide((prev)=>(prev + 1) % slides.length);\n    };\n    // Handle touch events for mobile swipe\n    const handleTouchStart = (e)=>{\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const distance = touchStart - touchEnd;\n        const isLeftSwipe = distance > 50;\n        const isRightSwipe = distance < -50;\n        if (isLeftSwipe) {\n            goToNextSlide();\n        } else if (isRightSwipe) {\n            goToPrevSlide();\n        }\n        setTouchStart(null);\n        setTouchEnd(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"relative overflow-hidden rounded-xl shadow-lg mt-2 bg-transparent from-theme-header to-theme-header/90\", className),\n        onMouseEnter: pauseAutoPlay,\n        onMouseLeave: resumeAutoPlay,\n        ref: containerRef,\n        onTouchStart: handleTouchStart,\n        onTouchMove: handleTouchMove,\n        onTouchEnd: handleTouchEnd,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-opacity-10 z-0 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 opacity-10 bg-[radial-gradient(#ffffff33_1px,transparent_1px)] bg-[size:20px_20px]\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex transition-transform duration-700 ease-out h-[280px] xs:h-[320px] sm:h-[380px] md:h-[420px] lg:h-[480px] xl:h-[520px]\",\n                style: {\n                    transform: `translateX(-${currentSlide * 100}%)`\n                },\n                children: slides.map((slide, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-w-full relative group cursor-pointer\",\n                        onClick: ()=>{\n                            try {\n                                window.location.href = slide.link || \"/shop\";\n                            } catch (error) {\n                                console.error(\"Navigation error:\", error);\n                                window.location.href = \"/shop\";\n                            }\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-[#8a6f4d]/20 to-[#d9c3a9]/10\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 right-0 bottom-0 w-[45%] xs:w-[42%] sm:w-[45%] md:w-[50%] flex items-center justify-center p-2 xs:p-3 sm:p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-full h-full max-w-[95%] max-h-[85%] xs:max-h-[90%]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: slide.image || '/home/<USER>',\n                                        alt: slide.title || 'Product Image',\n                                        fill: true,\n                                        sizes: \"(max-width: 640px) 50vw, (max-width: 768px) 45vw, (max-width: 1024px) 50vw, 50vw\",\n                                        className: \"object-contain object-center\",\n                                        priority: index === 0,\n                                        onError: (e)=>{\n                                            // Fallback to a placeholder if image fails to load\n                                            const imgElement = e.currentTarget;\n                                            if (!imgElement.src.includes('placeholder') && !imgElement.src.includes('image-1.png')) {\n                                                imgElement.src = '/home/<USER>';\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-black/50 via-black/30 to-transparent\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-[url('/patterns/dot-pattern.svg')] bg-repeat opacity-10\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white p-2 xs:p-3 sm:p-4 md:p-6 lg:p-8 xl:p-12 w-[55%] xs:w-[58%] sm:w-[55%] md:w-[50%] lg:w-[45%] xl:w-[40%]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-1.5 xs:gap-2 sm:gap-3 mb-1 xs:mb-1.5 sm:mb-2 md:mb-3\",\n                                            children: [\n                                                slide.brand && typeof slide.brand !== 'string' && (slide.brand?.image_url || slide.brand?.image) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 mt-0.5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-6 h-6 xs:w-8 xs:h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 lg:w-14 lg:h-14 overflow-hidden rounded-md border border-white/80 shadow-lg bg-white flex items-center justify-center p-0.5\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: slide.brand?.image_url || `${\"http://localhost:8000\" || 0}${slide.brand?.image}`,\n                                                            alt: `${slide.brand?.name} logo`,\n                                                            className: \"max-w-full max-h-full object-contain\",\n                                                            onError: (e)=>{\n                                                                // Hide the image on error\n                                                                const imgElement = e.currentTarget;\n                                                                imgElement.style.display = 'none';\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-sm xs:text-base sm:text-lg md:text-xl lg:text-2xl xl:text-3xl 2xl:text-4xl font-bold   [text-shadow:_0_1px_3px_rgb(0_0_0_/_50%)] leading-tight flex-1 pr-1\",\n                                                    children: slide.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs xs:text-xs sm:text-sm md:text-base lg:text-lg mb-1.5 xs:mb-2 sm:mb-3 md:mb-4 text-white/90   [text-shadow:_0_1px_2px_rgb(0_0_0_/_30%)] line-clamp-2 leading-snug max-w-[95%] break-words\",\n                                            children: slide.subtitle\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, this),\n                                        slide.code && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"inline-block bg-white/20 backdrop-blur-sm px-2 xs:px-2.5 sm:px-3 py-0.5 xs:py-1 rounded-full   text-xs xs:text-xs sm:text-sm md:text-base font-medium mb-1.5 xs:mb-2 sm:mb-3   [text-shadow:_0_1px_1px_rgb(0_0_0_/_20%)] whitespace-nowrap\",\n                                            children: slide.code\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1.5 xs:mt-2 sm:mt-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                href: slide.link || \"/shop\",\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                // Additional error handling can be added here if needed\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    className: \"bg-[#2ECC71] hover:bg-[#27AE60] text-white font-medium   px-2.5 xs:px-3 sm:px-4 md:px-6 py-1 xs:py-1.5 sm:py-2 rounded-full   transition-all duration-300 hover:shadow-lg hover:scale-105   text-xs xs:text-xs sm:text-sm md:text-base whitespace-nowrap\",\n                                                    children: slide.cta || \"Shop Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-0 left-0 right-[40%] xs:right-[42%] sm:right-[45%] md:right-[50%] p-1.5 xs:p-2 sm:p-3 bg-gradient-to-t from-black/60 to-transparent\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white text-xs xs:text-xs sm:text-sm\",\n                                    children: slide.specs && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mb-0.5 [text-shadow:_0_1px_1px_rgb(0_0_0_/_30%)] leading-tight break-words max-w-full opacity-90\",\n                                        children: slide.specs\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: (e)=>{\n                    e.stopPropagation();\n                    goToPrevSlide();\n                },\n                className: \"absolute left-2 xs:left-4 top-1/2 -translate-y-1/2 w-8 h-8 xs:w-10 xs:h-10 items-center justify-center   bg-theme-accent-primary hover:bg-theme-accent-hover text-white rounded-full shadow-md z-10 transition-all duration-300   hover:shadow-lg hover:scale-110 hidden xs:inline-flex\",\n                \"aria-label\": \"Previous slide\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 xs:h-6 xs:w-6 text-white transition-transform duration-300 hover:-translate-x-0.5\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                lineNumber: 244,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: (e)=>{\n                    e.stopPropagation();\n                    goToNextSlide();\n                },\n                className: \"absolute right-2 xs:right-4 top-1/2 -translate-y-1/2 w-8 h-8 xs:w-10 xs:h-10 items-center justify-center   bg-theme-accent-primary hover:bg-theme-accent-hover text-white rounded-full shadow-md z-10 transition-all duration-300   hover:shadow-lg hover:scale-110 hidden xs:inline-flex\",\n                \"aria-label\": \"Next slide\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 xs:h-6 xs:w-6 text-white transition-transform duration-300 hover:translate-x-0.5\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-2 xs:bottom-4 left-1/2 -translate-x-1/2 flex gap-1 xs:gap-2 z-10\",\n                children: slides.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>goToSlide(index),\n                        className: `w-2 h-2 xs:w-3 xs:h-3 rounded-full transition-all duration-300 ${index === currentSlide ? \"bg-theme-accent-secondary scale-110\" : \"bg-white/50 hover:bg-white/70 hover:scale-105\"}`,\n                        \"aria-label\": `Go to slide ${index + 1}`\n                    }, index, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/home/<USER>");

/***/ }),

/***/ "(ssr)/./components/home/<USER>":
/*!*******************************************!*\
  !*** ./components/home/<USER>
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HeroCarousel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AmazonStyleCarousel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AmazonStyleCarousel */ \"(ssr)/./components/home/<USER>");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/constant/urls */ \"(ssr)/./constant/urls.ts\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useApi */ \"(ssr)/./hooks/useApi.ts\");\n// \"use client\";\n// import { useState, useEffect } from \"react\";\n// import Image from \"next/image\";\n// import { ChevronLeft, ChevronRight } from \"lucide-react\";\n// export default function HeroCarousel() {\n//   const [current, setCurrent] = useState(0);\n//   useEffect(() => {\n//     const timer = setInterval(() => {\n//       setCurrent((prev) => (prev + 1) % slides.length);\n//     }, 5000);\n//     return () => clearInterval(timer);\n//   }, []);\n//   const prev = () => {\n//     setCurrent((curr) => (curr === 0 ? slides.length - 1 : curr - 1));\n//   };\n//   const next = () => {\n//     setCurrent((curr) => (curr + 1) % slides.length);\n//   };\n//   return (\n//     <div className=\"relative overflow-hidden\">\n//       <div\n//         className=\"flex transition-transform duration-500 ease-out\"\n//         style={{ transform: `translateX(-${current * 100}%)` }}\n//       >\n//         {slides.map((slide, index) => (\n//           <div\n//             key={index}\n//             className=\"min-w-full relative h-[400px] md:h-[600px]\"\n//           >\n//             <Image\n//               src={slide.image || \"/placeholder.svg\"}\n//               alt={slide.title}\n//               fill\n//               className=\"object-cover\"\n//               priority={index === 0}\n//             />\n//             <div className=\"absolute inset-0 flex items-center justify-center\">\n//               <div className=\"text-center\">\n//                 <h2 className=\"text-4xl md:text-6xl font-bold mb-4\">\n//                   {slide.title}\n//                 </h2>\n//                 <p className=\"text-xl md:text-2xl mb-2\">{slide.subtitle}</p>\n//                 {slide.code && (\n//                   <p className=\"text-lg md:text-xl font-medium\">{slide.code}</p>\n//                 )}\n//               </div>\n//             </div>\n//           </div>\n//         ))}\n//       </div>\n//       <button\n//         onClick={prev}\n//         className=\"absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 flex items-center justify-center bg-white/80 rounded-full shadow-md\"\n//       >\n//         <ChevronLeft className=\"h-6 w-6\" />\n//       </button>\n//       <button\n//         onClick={next}\n//         className=\"absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 flex items-center justify-center bg-white/80 rounded-full shadow-md\"\n//       >\n//         <ChevronRight className=\"h-6 w-6\" />\n//       </button>\n//       <div className=\"absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2\">\n//         {slides.map((_, index) => (\n//           <button\n//             key={index}\n//             onClick={() => setCurrent(index)}\n//             className={`w-2 h-2 rounded-full transition-colors ${\n//               index === current ? \"bg-white\" : \"bg-white/50\"\n//             }`}\n//           />\n//         ))}\n//       </div>\n//     </div>\n//   );\n// }\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// Fallback slides in case API fails\nconst fallbackSlides = [\n    {\n        image: \"/home/<USER>",\n        title: \"PRIMA Smart Lock\",\n        subtitle: \"Advanced security with fingerprint and PIN access\",\n        code: \"NEW ARRIVAL\",\n        cta: \"Shop Now\",\n        link: \"/shop\",\n        specs: \"Lock Size (inches): 9.6 x 5.6 • Min. Wooden Door Thickness: 3.2 cm Build\",\n        brand: \"PRIMA\"\n    },\n    {\n        image: \"/home/<USER>",\n        title: \"Qubo Smart Door Lock\",\n        subtitle: \"Keyless entry with advanced security features\",\n        cta: \"Explore Collection\",\n        link: \"/shop\",\n        specs: \"Compatible with standard door sizes • Multiple access methods\",\n        brand: \"Qubo\"\n    }\n];\nfunction HeroCarousel() {\n    // Use a ref to track if we've already fetched data to prevent continuous API calls\n    const dataFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const { data: featuredProducts, loading, read } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL || '');\n    const [slides, setSlides] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HeroCarousel.useEffect\": ()=>{\n            // Skip if we've already processed the data\n            if (dataFetchedRef.current && slides.length > 0) {\n                setIsLoading(false);\n                return;\n            }\n            const fetchFeaturedProducts = {\n                \"HeroCarousel.useEffect.fetchFeaturedProducts\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        // Only fetch if we don't already have featuredProducts data\n                        let productsArray = [];\n                        if (featuredProducts) {\n                            // Use existing data if available\n                            if (Array.isArray(featuredProducts)) {\n                                productsArray = featuredProducts;\n                            } else if (featuredProducts.results && Array.isArray(featuredProducts.results)) {\n                                productsArray = featuredProducts.results;\n                            } else if (featuredProducts.products && Array.isArray(featuredProducts.products)) {\n                                productsArray = featuredProducts.products;\n                            }\n                        } else {\n                            // Fetch only if necessary\n                            const result = await read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.FUTURED_PRODUCTS);\n                            // Process the API response\n                            if (Array.isArray(result)) {\n                                productsArray = result;\n                            } else if (result && result.results && Array.isArray(result.results)) {\n                                productsArray = result.results;\n                            } else if (result && result.products && Array.isArray(result.products)) {\n                                productsArray = result.products;\n                            }\n                        }\n                        // Take top 5 products\n                        const topProducts = productsArray.slice(0, 5);\n                        if (topProducts.length > 0) {\n                            // Transform products into slides\n                            const productSlides = topProducts.map({\n                                \"HeroCarousel.useEffect.fetchFeaturedProducts.productSlides\": (product)=>{\n                                    // Ensure we have a valid image URL\n                                    let imageUrl = '/home/<USER>'; // Default fallback image\n                                    if (product.image) {\n                                        if (product.image.startsWith('http')) {\n                                            imageUrl = product.image;\n                                        } else if (_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL) {\n                                            // Make sure the image path is properly formatted\n                                            const imagePath = product.image.startsWith('/') ? product.image : `/${product.image}`;\n                                            imageUrl = _constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL + imagePath;\n                                        }\n                                    }\n                                    // Clean up product description to avoid HTML tags\n                                    let cleanDescription = product.description || \"Explore our premium collection\";\n                                    cleanDescription = cleanDescription.replace(/<[^>]*>?/gm, '');\n                                    // Create specifications string from product attributes\n                                    let specs = '';\n                                    if (product.size) specs += `Size: ${product.size} `;\n                                    if (product.dimensions) specs += `Dimensions: ${product.dimensions} `;\n                                    if (product.weight) specs += `Weight: ${product.weight} `;\n                                    if (product.material) specs += `Material: ${product.material} `;\n                                    if (product.color) specs += `Color: ${product.color} `;\n                                    // For products like the ones in the image (door locks, etc.)\n                                    if (product.category && product.category.name && product.category.name.toLowerCase().includes('lock')) {\n                                        specs = `Lock Size: ${product.dimensions || '9.6 x 5.6'} • Min. Door Thickness: ${product.min_thickness || '3.2 cm'} Build`;\n                                    }\n                                    return {\n                                        image: imageUrl,\n                                        title: product.name || \"Featured Product\",\n                                        subtitle: cleanDescription.substring(0, 100),\n                                        code: product.discount_percentage ? `SAVE ${product.discount_percentage}%` : undefined,\n                                        cta: \"Shop Now\",\n                                        link: `/product/${product.slug}`,\n                                        specs: specs,\n                                        brand: product.brand || undefined\n                                    };\n                                }\n                            }[\"HeroCarousel.useEffect.fetchFeaturedProducts.productSlides\"]);\n                            setSlides(productSlides);\n                        } else {\n                            // Use fallback slides if no products found\n                            setSlides(fallbackSlides);\n                        }\n                        // Mark as fetched to prevent continuous API calls\n                        dataFetchedRef.current = true;\n                    } catch (error) {\n                        console.error(\"Error fetching featured products for carousel:\", error);\n                        setSlides(fallbackSlides);\n                        dataFetchedRef.current = true; // Mark as fetched even on error to prevent continuous retries\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"HeroCarousel.useEffect.fetchFeaturedProducts\"];\n            fetchFeaturedProducts();\n        }\n    }[\"HeroCarousel.useEffect\"], [\n        featuredProducts,\n        read\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full mb-4 sm:mb-8 pt-2\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"rounded-xl overflow-hidden shadow-lg h-[250px] xs:h-[300px] sm:h-[350px] md:h-[400px] lg:h-[450px] xl:h-[500px] bg-gray-100 animate-pulse\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full w-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 rounded-full border-4 border-theme-accent-primary border-t-transparent animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\hero-carousel.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\hero-carousel.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\hero-carousel.tsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\hero-carousel.tsx\",\n            lineNumber: 234,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full mb-4 sm:mb-8 pt-2\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AmazonStyleCarousel__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            slides: slides.length > 0 ? slides : fallbackSlides,\n            autoplayInterval: 5000,\n            className: \"rounded-xl overflow-hidden shadow-lg transform transition-all duration-700 hover:shadow-xl\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\hero-carousel.tsx\",\n            lineNumber: 246,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\hero-carousel.tsx\",\n        lineNumber: 245,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/home/<USER>");

/***/ })

};
;