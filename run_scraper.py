#!/usr/bin/env python3
"""
Simple script to run the product scraper
"""

import sys
import os
from product_scraper import ProductScraper

def main():
    print("Starting Product Scraper...")
    print("=" * 50)
    
    # Check if CSV file exists
    csv_file = "GT Pricelist effective 1st March shared.csv"
    if not os.path.exists(csv_file):
        print(f"Error: CSV file '{csv_file}' not found!")
        print("Please make sure the CSV file is in the same directory as this script.")
        return
    
    try:
        # Initialize scraper
        scraper = ProductScraper()
        
        # Process products (limit to first 5 for testing)
        print(f"Processing products from {csv_file}...")
        products = scraper.process_csv_products(csv_file)
        
        if products:
            # Save to JSON
            output_file = "products_output.json"
            scraper.save_products_json(products, output_file)
            
            print(f"\n✅ Successfully processed {len(products)} products!")
            print(f"📁 Output saved to: {output_file}")
            
            # Show sample product
            print("\n" + "=" * 50)
            print("SAMPLE PRODUCT:")
            print("=" * 50)
            sample = products[0]
            print(f"Name: {sample.get('name', 'N/A')}")
            print(f"Brand: {sample.get('brand', 'N/A')}")
            print(f"Category: {sample.get('category', 'N/A')}")
            print(f"Sell Price: ₹{sample.get('sell_price', 0)}")
            print(f"Cost Price: ₹{sample.get('cost_price', 0)}")
            print(f"Discount: {sample.get('discount', 0)}%")
            print(f"Rating: {sample.get('rating', 'N/A')}")
            print(f"Images: {len(sample.get('images', []))} found")
            print(f"Description: {sample.get('description', 'N/A')[:100]}...")
            
        else:
            print("❌ No products were processed successfully.")
            print("This might be due to network issues or website blocking.")
            
    except Exception as e:
        print(f"❌ Error running scraper: {e}")
        print("Please check your internet connection and try again.")

if __name__ == "__main__":
    main()
