import React, { useState, useEffect } from "react";
import { Card, CardContent } from "../ui/card";
import { Button } from "../ui/button";
import { Eye, Heart, ShoppingCart } from "lucide-react";
import { ProductType } from "../../types/product";
import { useToast } from "../../hooks/use-toast";
import { usePathname, useRouter } from "next/navigation";
import useApi from "../../hooks/useApi";
import {
  ADD_TO_CART,
  ADD_TO_WISHLIST,
  MAIN_URL,
  REMOVE_FROM_WISHLIST,
} from "../../constant/urls";
import { useSession } from "next-auth/react";
import { motion } from "framer-motion";
import useStorage from "../../hooks/useStorage";
import { getImageUrl } from "../../utils/imageUtils";

const ProductCard = (props: ProductType) => {
  const { image, name, slug, price, rating, id, category, brand } = props;
  const { toast } = useToast();
  const router = useRouter();
  const { create, loading } = useApi(MAIN_URL || '');
  const { create: createWishlist } = useApi(MAIN_URL || '');
  const { remove } = useApi(MAIN_URL || '');
  const { status } = useSession();
  const pathName = usePathname();
  const [wishlistIds, setWishlistIds] = useState<string | null>(null);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const storage = useStorage('local');

  // Check if product is in wishlist
  useEffect(() => {
    const storedWishlistIds = storage.getItem("wishlistIds");
    setWishlistIds(storedWishlistIds);
  }, [storage]);

  const isAdded = wishlistIds ? wishlistIds.includes(id.toString()) : false;

  // Handle add to cart
  const handleAddToCart = async (e: React.MouseEvent) => {
    e.stopPropagation();

    if (status === "unauthenticated") {
      router.push(`/auth/login?callbackUrl=%2F${pathName}`);
      return;
    }

    setIsAddingToCart(true);

    try {
      await create(ADD_TO_CART, {
        product_id: id,
        quantity: 1,
      });

      toast({
        variant: "success",
        title: "Added to Cart",
        description: `${name} has been added to your cart`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add product to cart",
        variant: "destructive",
      });
    } finally {
      setIsAddingToCart(false);
    }
  };

  // Handle quick view
  const handleQuickView = (e: React.MouseEvent) => {
    e.stopPropagation();
    router.push(`/product/${slug}`);
  };

  // Handle add to wishlist
  const handleAddToWishlist = async (e: React.MouseEvent) => {
    e.stopPropagation();

    if (status === "unauthenticated") {
      router.push(`/auth/login?callbackUrl=%2F${pathName}`);
      return;
    }

    try {
      await createWishlist(ADD_TO_WISHLIST, {
        product_id: id,
      });

      // Update local storage
      const currentIds = storage.getItem("wishlistIds") || "";
      const newIds = currentIds ? `${currentIds},${id}` : `${id}`;
      storage.setItem("wishlistIds", newIds);
      setWishlistIds(newIds);

      toast({
        variant: "success",
        title: "Added to Wishlist",
        description: `${name} has been added to your wishlist`,
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to add product to wishlist",
      });
    }
  };

  // Handle remove from wishlist
  const handleRemoveFromWishlist = async (e: React.MouseEvent) => {
    e.stopPropagation();

    try {
      await remove(`${REMOVE_FROM_WISHLIST}${id}/`);

      // Update local storage
      const currentIds = storage.getItem("wishlistIds") || "";
      const idsArray = currentIds.split(",");
      const filteredIds = idsArray.filter(item => item !== id.toString()).join(",");
      storage.setItem("wishlistIds", filteredIds);
      setWishlistIds(filteredIds);

      toast({
        variant: "info",
        title: "Removed from Wishlist",
        description: `${name} has been removed from your wishlist`,
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to remove product from wishlist",
      });
    }
  };

  // Navigate to product page when card is clicked
  const navigateToProduct = () => {
    router.push(`/product/${slug}`);
  };

  return (
    <Card
      className="overflow-hidden h-full rounded-lg xs:rounded-xl flex flex-col relative glass-product-card cursor-pointer"
      onClick={navigateToProduct}
    >
      <CardContent className="p-0 h-full flex flex-col">
        {/* Product Image Container - Improved for Mobile */}
        <div className="relative overflow-hidden bg-white/5 backdrop-blur-sm" style={{ paddingBottom: "100%" }}>
          {/* Brand Badge - Image only */}
          {brand && typeof brand !== 'string' && (brand?.image_url || brand?.image) && (
            <div className="absolute top-2 left-2 z-10">
              <div className="w-8 h-8 xs:w-10 xs:h-10 overflow-hidden rounded-md border border-white/20 shadow-md bg-white/10 backdrop-blur-sm flex items-center justify-center p-1">
                <img
                  src={brand?.image_url || `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}${brand?.image}`}
                  alt={`${brand?.name} logo`}
                  className="max-w-full max-h-full object-contain"
                  onError={(e) => {
                    // Hide the image on error
                    const imgElement = e.currentTarget as HTMLImageElement;
                    imgElement.style.display = 'none';
                  }}
                />
              </div>
            </div>
          )}

          {/* Loading Spinner */}
          {!imageLoaded && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-6 h-6 sm:w-8 sm:h-8 border-3 sm:border-4 border-theme-accent-primary/30 border-t-theme-accent-primary rounded-full animate-spin"></div>
            </div>
          )}

          {/* Product Image */}
          <div className="absolute inset-0 flex items-center justify-center p-2 xs:p-3 sm:p-4">
            <img
              src={getImageUrl(image, '/assets/products/product-placeholder.svg')}
              alt={name}
              className={`max-w-full max-h-full object-contain transition-all duration-300 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
              onLoad={() => setImageLoaded(true)}
              onError={(e) => {
                const imgElement = e.target as HTMLImageElement;
                // Try to load a category-specific image first
                if (!imgElement.src.includes('/assets/products/')) {
                  // Extract category name if available
                  const categoryName = typeof props?.category?.name === 'string'
                    ? props.category.name.toLowerCase().replace(/\s+/g, '-')
                    : 'product';

                  // Try to load a category-specific placeholder
                  imgElement.src = `/assets/products/${categoryName}.svg`;

                  // Add a second error handler for the category placeholder
                  imgElement.onerror = () => {
                    // If category placeholder fails, use generic product placeholder
                    imgElement.src = '/assets/products/product-placeholder.svg';
                    imgElement.onerror = null; // Prevent infinite error loop
                  };
                }
                setImageLoaded(true);
              }}
              style={{ maxHeight: "85%", maxWidth: "85%" }}
            />
          </div>
        </div>

        {/* Product Info Section - Improved for Mobile */}
        <div className="p-2 xs:p-3 sm:p-4 flex-grow flex flex-col justify-between bg-white/5 backdrop-blur-sm">
          {/* Product Name */}
          <h3 className="font-medium text-white line-clamp-2 text-left min-h-[2.5rem] text-xs xs:text-sm sm:text-base mb-1 xs:mb-2">
            {name}
          </h3>

          {/* Price */}
          <div className="mt-auto">
            <div className="flex items-center justify-between mb-2 xs:mb-3">
              <span className="font-bold text-blue-400 text-sm xs:text-base sm:text-lg">₹{price}</span>
            </div>

            {/* Action Buttons - Responsive Layout */}
            <div className="flex flex-col xs:flex-row items-center gap-2">
              {/* Primary Button - Full Width on Mobile, Flex-1 on Larger Screens */}
              <Button
                variant="outline"
                size="sm"
                className="w-full xs:flex-1 bg-theme-accent-primary text-white hover:bg-theme-accent-hover border-none h-9"
                onClick={handleAddToCart}
                disabled={isAddingToCart || loading}
              >
                {isAddingToCart ? (
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-1"></div>
                ) : (
                  <ShoppingCart className="h-4 w-4 mr-1" />
                )}
                <span className="text-xs sm:text-sm whitespace-nowrap">Add to Cart</span>
              </Button>

              {/* Secondary Buttons - Row on Mobile, Inline on Larger Screens */}
              <div className="flex items-center justify-center gap-2 w-full xs:w-auto mt-2 xs:mt-0">
                <Button
                  variant="outline"
                  size="icon"
                  className="bg-white/10 border-white/20 hover:bg-white/20 text-white h-9 w-9 backdrop-blur-sm"
                  onClick={isAdded ? handleRemoveFromWishlist : handleAddToWishlist}
                >
                  <Heart
                    className="h-4 w-4"
                    fill={isAdded ? "rgb(236 72 153)" : "none"}
                    stroke={isAdded ? "rgb(236 72 153)" : "currentColor"}
                  />
                </Button>

                <Button
                  variant="outline"
                  size="icon"
                  className="bg-white/10 border-white/20 hover:bg-white/20 text-white h-9 w-9 backdrop-blur-sm"
                  onClick={handleQuickView}
                >
                  <Eye className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProductCard;
