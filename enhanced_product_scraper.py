import pandas as pd
import requests
from bs4 import BeautifulSoup
import json
import time
import random
from urllib.parse import quote_plus
import re
import logging
from typing import List, Dict, Optional

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedProductScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
    def clean_price(self, price_str: str) -> float:
        """Clean and convert price string to float"""
        if not price_str or price_str.strip() == '-':
            return 0.0
        # Remove commas, spaces, quotes, and currency symbols
        cleaned = re.sub(r'[,\s₹$"\']', '', str(price_str))
        try:
            return float(cleaned)
        except ValueError:
            return 0.0
    
    def search_google_shopping(self, product_name: str) -> Dict:
        """Search Google Shopping for product images and details"""
        try:
            # Clean product name for search
            search_query = f"{product_name} Bosch tool"
            search_url = f"https://www.google.com/search?q={quote_plus(search_query)}&tbm=shop"
            
            response = self.session.get(search_url, timeout=15)
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Extract product information
                product_data = {}
                
                # Find product containers
                product_containers = soup.find_all('div', class_='sh-dgr__content')
                
                if product_containers:
                    first_product = product_containers[0]
                    
                    # Extract image
                    img_elem = first_product.find('img')
                    if img_elem and img_elem.get('src'):
                        product_data['images'] = [img_elem['src']]
                    
                    # Extract title
                    title_elem = first_product.find('h3') or first_product.find('a')
                    if title_elem:
                        product_data['name'] = title_elem.get_text().strip()
                
                return product_data
                
        except Exception as e:
            logger.error(f"Error searching Google Shopping: {e}")
        
        return {}
    
    def search_flipkart_enhanced(self, product_name: str) -> Dict:
        """Enhanced Flipkart search with better selectors"""
        try:
            # Create multiple search variations
            search_variations = [
                f"{product_name} Bosch",
                f"Bosch {product_name}",
                product_name.replace('-', ' '),
                product_name.split()[0] if product_name.split() else product_name
            ]
            
            for search_query in search_variations:
                search_url = f"https://www.flipkart.com/search?q={quote_plus(search_query)}"
                
                try:
                    response = self.session.get(search_url, timeout=15)
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        
                        # Multiple strategies to find products
                        product_links = []
                        
                        # Strategy 1: Look for product containers
                        containers = soup.find_all('div', {'data-id': True})
                        for container in containers:
                            link = container.find('a', href=re.compile(r'/p/'))
                            if link:
                                product_links.append(link)
                        
                        # Strategy 2: Direct link search
                        if not product_links:
                            product_links = soup.find_all('a', href=re.compile(r'/p/'))
                        
                        # Try to get details from first few products
                        for link in product_links[:3]:
                            href = link.get('href', '')
                            if '/p/' in href:
                                product_url = "https://www.flipkart.com" + href
                                details = self.get_flipkart_details_enhanced(product_url)
                                if details and details.get('images'):
                                    return details
                
                except Exception as e:
                    logger.warning(f"Error with search variation '{search_query}': {e}")
                    continue
                
                # Add delay between searches
                time.sleep(1)
            
        except Exception as e:
            logger.error(f"Error in enhanced Flipkart search: {e}")
        
        return {}
    
    def get_flipkart_details_enhanced(self, url: str) -> Dict:
        """Get enhanced product details from Flipkart"""
        try:
            response = self.session.get(url, timeout=15)
            if response.status_code != 200:
                return {}
            
            soup = BeautifulSoup(response.content, 'html.parser')
            product_data = {}
            
            # Enhanced name extraction
            name_selectors = [
                'span.B_NuCI',
                'h1.yhB1nd', 
                'h1._35KyD6',
                '.B_NuCI',
                'span.VU-ZEz',
                'h1'
            ]
            
            for selector in name_selectors:
                elem = soup.select_one(selector)
                if elem and elem.get_text().strip():
                    product_data['name'] = elem.get_text().strip()
                    break
            
            # Enhanced description extraction
            desc_selectors = [
                'div._1mXcCf RichTextEditor__root',
                'div._1mXcCf',
                'div._3WHvuP', 
                'div.yN+eNk',
                'div._1AN87F',
                'div[data-testid="product-description"]'
            ]
            
            for selector in desc_selectors:
                elem = soup.select_one(selector)
                if elem and elem.get_text().strip():
                    desc_text = elem.get_text().strip()
                    if len(desc_text) > 50:  # Only use substantial descriptions
                        product_data['description'] = desc_text[:500]  # Limit length
                        break
            
            # Enhanced image extraction
            images = []
            
            # Multiple image selectors
            img_selectors = [
                'img._396cs4',
                'img._2r_T1I', 
                'img.q6DClP',
                'img[src*="rukminim"]',
                'img[data-src*="rukminim"]'
            ]
            
            for selector in img_selectors:
                img_elements = soup.select(selector)
                for img in img_elements:
                    src = img.get('src') or img.get('data-src')
                    if src and ('rukminim' in src or 'flixcart' in src):
                        # Convert to high quality
                        if 'rukminim' in src:
                            high_quality = re.sub(r'/\d+/\d+/', '/1080/1080/', src)
                            high_quality = re.sub(r'\?.*', '?q=70&crop=false', high_quality)
                            images.append(high_quality)
                
                if images:
                    break
            
            # Remove duplicates and limit
            product_data['images'] = list(dict.fromkeys(images))[:8]
            
            # Rating extraction
            rating_selectors = [
                'div._3LWZlK',
                'span._1lRcqv',
                'div[data-testid="rating"]'
            ]
            
            for selector in rating_selectors:
                elem = soup.select_one(selector)
                if elem:
                    rating_text = elem.get_text().strip()
                    rating_match = re.search(r'(\d+\.?\d*)', rating_text)
                    if rating_match:
                        product_data['rating'] = rating_match.group(1)
                        break
            
            # Brand extraction
            if 'name' in product_data:
                # Try to extract brand from product name
                name_lower = product_data['name'].lower()
                if 'bosch' in name_lower:
                    product_data['brand'] = 'Bosch'
                else:
                    # Extract first word as potential brand
                    first_word = product_data['name'].split()[0]
                    if first_word.isalpha():
                        product_data['brand'] = first_word
            
            return product_data
            
        except Exception as e:
            logger.error(f"Error getting enhanced Flipkart details: {e}")
            return {}
    
    def get_bosch_official_info(self, product_name: str, part_no: str) -> Dict:
        """Try to get information from Bosch official sources"""
        try:
            # Search Bosch India website
            search_query = f"{product_name} {part_no}"
            search_url = f"https://www.bosch.co.in/search/?q={quote_plus(search_query)}"
            
            response = self.session.get(search_url, timeout=15)
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                product_data = {}
                
                # Look for product information
                product_containers = soup.find_all('div', class_='product-item')
                
                if product_containers:
                    first_product = product_containers[0]
                    
                    # Extract image
                    img_elem = first_product.find('img')
                    if img_elem and img_elem.get('src'):
                        img_src = img_elem['src']
                        if not img_src.startswith('http'):
                            img_src = 'https://www.bosch.co.in' + img_src
                        product_data['images'] = [img_src]
                    
                    # Extract description
                    desc_elem = first_product.find('p') or first_product.find('div', class_='description')
                    if desc_elem:
                        product_data['description'] = desc_elem.get_text().strip()
                
                return product_data
                
        except Exception as e:
            logger.error(f"Error searching Bosch official: {e}")
        
        return {}
    
    def generate_realistic_images(self, product_name: str, part_no: str) -> List[str]:
        """Generate more realistic placeholder images"""
        # Create different placeholder images based on product type
        product_type = "tool"
        if any(word in product_name.lower() for word in ['drill', 'driver']):
            product_type = "drill"
        elif any(word in product_name.lower() for word in ['grinder', 'angle']):
            product_type = "grinder"
        elif any(word in product_name.lower() for word in ['battery', 'charger']):
            product_type = "battery"
        
        images = [
            f"https://via.placeholder.com/600x600/0066cc/ffffff?text=Bosch+{product_type.title()}",
            f"https://via.placeholder.com/600x600/003d7a/ffffff?text={part_no}",
            f"https://via.placeholder.com/600x600/004d94/ffffff?text=Professional+Grade"
        ]
        
        return images
    
    def process_csv_enhanced(self, csv_file: str, max_products: int = 20) -> List[Dict]:
        """Enhanced CSV processing with better data extraction"""
        try:
            # Read CSV with multiple encoding attempts
            encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
            df = None
            
            for encoding in encodings:
                try:
                    df = pd.read_csv(csv_file, encoding=encoding)
                    logger.info(f"Successfully read CSV with {encoding} encoding")
                    break
                except UnicodeDecodeError:
                    continue
            
            if df is None:
                raise Exception("Could not read CSV file with any encoding")
            
            products = []
            
            for index, row in df.iterrows():
                if pd.isna(row.get('Material Description')) or row.get('Material Description') == '':
                    continue
                
                logger.info(f"Processing product {index + 1}: {row['Material Description']}")
                
                # Extract basic information
                product_name = str(row['Material Description']).strip()
                part_no = str(row.get('Part No', '')).strip()
                
                # Create base product object
                product = {
                    'name': product_name,
                    'sell_price': self.clean_price(row.get('Selling Price', 0)),
                    'cost_price': self.clean_price(row.get('MRP', 0)),
                    'discount': 0.0,
                    'description': f"Professional grade {product_name} from Bosch. High-quality tool designed for durability and performance.",
                    'rating': '4.0',
                    'brand': 'Bosch',
                    'category': self.categorize_product(row.get('Product Group', ''), product_name),
                    'images': self.generate_realistic_images(product_name, part_no)
                }
                
                # Calculate discount
                if product['cost_price'] > 0 and product['sell_price'] > 0:
                    discount = ((product['cost_price'] - product['sell_price']) / product['cost_price']) * 100
                    product['discount'] = round(discount, 1)
                
                # Try to get enhanced product information
                enhanced_data = None
                
                # Try Flipkart first
                enhanced_data = self.search_flipkart_enhanced(product_name)
                
                # Try Bosch official if Flipkart fails
                if not enhanced_data or not enhanced_data.get('images'):
                    bosch_data = self.get_bosch_official_info(product_name, part_no)
                    if bosch_data:
                        enhanced_data = {**enhanced_data, **bosch_data} if enhanced_data else bosch_data
                
                # Try Google Shopping as fallback
                if not enhanced_data or not enhanced_data.get('images'):
                    google_data = self.search_google_shopping(product_name)
                    if google_data:
                        enhanced_data = {**enhanced_data, **google_data} if enhanced_data else google_data
                
                # Merge enhanced data
                if enhanced_data:
                    if enhanced_data.get('name') and len(enhanced_data['name']) > len(product['name']):
                        product['name'] = enhanced_data['name']
                    
                    if enhanced_data.get('description'):
                        product['description'] = enhanced_data['description']
                    
                    if enhanced_data.get('rating'):
                        product['rating'] = enhanced_data['rating']
                    
                    if enhanced_data.get('brand'):
                        product['brand'] = enhanced_data['brand']
                    
                    if enhanced_data.get('images'):
                        product['images'] = enhanced_data['images']
                
                products.append(product)
                
                # Add delay to avoid being blocked
                time.sleep(random.uniform(2, 5))
                
                # Limit products for testing
                if len(products) >= max_products:
                    break
            
            return products
            
        except Exception as e:
            logger.error(f"Error processing CSV: {e}")
            return []
    
    def categorize_product(self, product_group: str, material_description: str) -> str:
        """Enhanced product categorization"""
        category_mapping = {
            'Drill Drivers': 'Power Tools',
            'Impact Drill Drivers': 'Power Tools', 
            'Impact Wrench/Wrench': 'Power Tools',
            'Rotary Hammers': 'Power Tools',
            'Small Angle Grinder': 'Power Tools',
            'Large Angle Grinder': 'Power Tools',
            'Vacuum cleaners': 'Cleaning Equipment',
            'Battery-Packs': 'Accessories',
            'Chargers': 'Accessories',
            'Carrying cases': 'Accessories',
            'Circular Saws': 'Power Tools',
            'Jigsaws': 'Power Tools',
            'All purpose/Recipsaw': 'Power Tools',
            'Planers': 'Power Tools',
            'Random Orbit Sanders': 'Power Tools'
        }
        
        return category_mapping.get(product_group, 'Tools & Equipment')

def main():
    scraper = EnhancedProductScraper()
    
    # Process the CSV file
    csv_file = "GT Pricelist effective 1st March shared.csv"
    products = scraper.process_csv_enhanced(csv_file, max_products=15)
    
    # Save to JSON file
    output_data = {'products': products}
    
    with open('products_output.json', 'w', encoding='utf-8') as f:
        json.dump(output_data, f, indent=2, ensure_ascii=False)
    
    logger.info(f"Successfully processed {len(products)} products")
    logger.info("Results saved to products_output.json")
    
    # Print sample output
    if products:
        print("\nSample product:")
        print(json.dumps(products[0], indent=2, ensure_ascii=False))

if __name__ == "__main__":
    main()
