import pandas as pd
import requests
from bs4 import BeautifulSoup
import json
import time
import random
from urllib.parse import quote_plus
import re
from typing import List, Dict, Optional

class ProductScraper:
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def clean_price(self, price_str: str) -> float:
        """Clean and convert price string to float"""
        if not price_str or price_str.strip() == '-':
            return 0.0
        # Remove commas, spaces, and currency symbols
        cleaned = re.sub(r'[,\s₹$]', '', str(price_str))
        try:
            return float(cleaned)
        except ValueError:
            return 0.0
    
    def search_flipkart(self, product_name: str) -> Dict:
        """Search for product on Flipkart"""
        try:
            # Clean product name for better search results
            clean_name = re.sub(r'[^\w\s]', ' ', product_name).strip()
            search_url = f"https://www.flipkart.com/search?q={quote_plus(clean_name)}"

            response = self.session.get(search_url, timeout=15)

            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')

                # Multiple selectors for product links
                product_links = []
                link_selectors = [
                    'a[href*="/p/"]',
                    'a._1fQZEK',
                    'a.s1Q9rs',
                    'a._2rpwqI'
                ]

                for selector in link_selectors:
                    links = soup.select(selector)
                    if links:
                        product_links = links
                        break

                if product_links:
                    # Get the first valid product link
                    for link in product_links[:3]:  # Try first 3 links
                        href = link.get('href', '')
                        if '/p/' in href:
                            product_url = "https://www.flipkart.com" + href
                            details = self.get_flipkart_product_details(product_url)
                            if details:  # Return first successful result
                                return details

        except Exception as e:
            print(f"Error searching Flipkart for {product_name}: {e}")

        return {}
    
    def get_flipkart_product_details(self, url: str) -> Dict:
        """Get detailed product information from Flipkart product page"""
        try:
            response = self.session.get(url, timeout=10)
            if response.status_code != 200:
                return {}
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract product details
            product_data = {}
            
            # Product name
            name_selectors = [
                'span.B_NuCI',
                'h1.yhB1nd',
                'h1._35KyD6',
                '.B_NuCI'
            ]
            for selector in name_selectors:
                name_elem = soup.select_one(selector)
                if name_elem:
                    product_data['name'] = name_elem.get_text().strip()
                    break
            
            # Description
            desc_selectors = [
                'div._1mXcCf',
                'div._3WHvuP',
                'div.yN+eNk',
                'div._1AN87F'
            ]
            for selector in desc_selectors:
                desc_elem = soup.select_one(selector)
                if desc_elem:
                    product_data['description'] = desc_elem.get_text().strip()
                    break
            
            # Rating
            rating_selectors = [
                'div._3LWZlK',
                'div._3LWZlK span',
                'span._1lRcqv'
            ]
            for selector in rating_selectors:
                rating_elem = soup.select_one(selector)
                if rating_elem:
                    rating_text = rating_elem.get_text().strip()
                    rating_match = re.search(r'(\d+\.?\d*)', rating_text)
                    if rating_match:
                        product_data['rating'] = rating_match.group(1)
                        break
            
            # Images
            images = []
            img_selectors = [
                'img._396cs4',
                'img._2r_T1I',
                'img.q6DClP'
            ]
            
            for selector in img_selectors:
                img_elements = soup.select(selector)
                for img in img_elements:
                    src = img.get('src') or img.get('data-src')
                    if src and 'image' in src:
                        # Convert to high quality image URL
                        if 'flixcart.com' in src:
                            high_quality_url = re.sub(r'/\d+/\d+/', '/1080/1080/', src)
                            high_quality_url = re.sub(r'\?.*', '?q=70&crop=false', high_quality_url)
                            images.append(high_quality_url)
                
                if images:
                    break
            
            product_data['images'] = list(set(images))[:8]  # Limit to 8 unique images
            
            # Brand (try to extract from name or description)
            if 'name' in product_data:
                brand_match = re.search(r'^([A-Za-z]+)', product_data['name'])
                if brand_match:
                    product_data['brand'] = brand_match.group(1)
            
            return product_data
            
        except Exception as e:
            print(f"Error getting Flipkart product details: {e}")
            return {}
    
    def search_amazon(self, product_name: str) -> Dict:
        """Search for product on Amazon (basic implementation)"""
        try:
            clean_name = re.sub(r'[^\w\s]', ' ', product_name).strip()
            search_url = f"https://www.amazon.in/s?k={quote_plus(clean_name)}"
            response = self.session.get(search_url, timeout=15)

            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')

                # Find first product
                product_containers = soup.find_all('div', {'data-component-type': 's-search-result'})
                if product_containers:
                    first_product = product_containers[0]

                    # Extract basic info
                    product_data = {}

                    # Name
                    name_selectors = [
                        'h2.a-size-mini a span',
                        'h2 a span',
                        '.a-link-normal span'
                    ]

                    for selector in name_selectors:
                        name_elem = first_product.select_one(selector)
                        if name_elem:
                            product_data['name'] = name_elem.get_text().strip()
                            break

                    # Image
                    img_elem = first_product.find('img', class_='s-image')
                    if img_elem:
                        img_src = img_elem.get('src', '')
                        if img_src:
                            product_data['images'] = [img_src]

                    # Rating
                    rating_elem = first_product.find('span', class_='a-icon-alt')
                    if rating_elem:
                        rating_text = rating_elem.get_text()
                        rating_match = re.search(r'(\d+\.?\d*)', rating_text)
                        if rating_match:
                            product_data['rating'] = rating_match.group(1)

                    return product_data

        except Exception as e:
            print(f"Error searching Amazon for {product_name}: {e}")

        return {}

    def generate_default_images(self, product_name: str) -> List[str]:
        """Generate placeholder images for products without images"""
        # This is a placeholder - in a real implementation, you might want to:
        # 1. Use a stock photo API
        # 2. Generate placeholder images
        # 3. Use manufacturer's official images

        placeholder_images = [
            "https://via.placeholder.com/400x400/0066cc/ffffff?text=Bosch+Tool",
            "https://via.placeholder.com/400x400/003d7a/ffffff?text=Professional+Grade",
            "https://via.placeholder.com/400x400/004d94/ffffff?text=Power+Tool"
        ]

        return placeholder_images[:3]
    
    def categorize_product(self, product_group: str, material_description: str) -> str:
        """Categorize product based on group and description"""
        category_mapping = {
            'Drill Drivers': 'Power Tools',
            'Impact Drill Drivers': 'Power Tools',
            'Impact Wrench/Wrench': 'Power Tools',
            'Rotary Hammers': 'Power Tools',
            'Small Angle Grinder': 'Power Tools',
            'Large Angle Grinder': 'Power Tools',
            'Vacuum cleaners': 'Cleaning Equipment',
            'Battery-Packs': 'Accessories',
            'Chargers': 'Accessories',
            'Carrying cases': 'Accessories'
        }
        
        return category_mapping.get(product_group, 'Tools & Equipment')
    
    def process_csv_products(self, csv_file: str) -> List[Dict]:
        """Process products from CSV file"""
        try:
            # Try different encodings
            encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
            df = None

            for encoding in encodings:
                try:
                    df = pd.read_csv(csv_file, encoding=encoding)
                    print(f"Successfully read CSV with {encoding} encoding")
                    break
                except UnicodeDecodeError:
                    continue

            if df is None:
                raise Exception("Could not read CSV file with any encoding")

            products = []
            
            for index, row in df.iterrows():
                if pd.isna(row.get('Material Description')) or row.get('Material Description') == '':
                    continue
                
                print(f"Processing product {index + 1}: {row['Material Description']}")
                
                # Basic product info from CSV
                product = {
                    'name': row['Material Description'],
                    'sell_price': self.clean_price(row.get('Selling Price', 0)),
                    'cost_price': self.clean_price(row.get('MRP', 0)),
                    'list_price': self.clean_price(row.get('List Price', 0)),
                    'category': self.categorize_product(row.get('Product Group', ''), row['Material Description']),
                    'part_no': row.get('Part No', ''),
                    'hsn_code': row.get('HSN Code', ''),
                    'power_source': row.get('Power Source', ''),
                    'user_group': row.get('User Group', '')
                }
                
                # Calculate discount percentage
                if product['cost_price'] > 0 and product['sell_price'] > 0:
                    discount = ((product['cost_price'] - product['sell_price']) / product['cost_price']) * 100
                    product['discount'] = round(discount, 1)
                else:
                    product['discount'] = 0.0
                
                # Search for additional product details online
                search_results = self.search_flipkart(row['Material Description'])

                if not search_results:
                    # Try Amazon if Flipkart fails
                    search_results = self.search_amazon(row['Material Description'])

                # Merge online data with CSV data
                if search_results:
                    # Use online name if available and more descriptive
                    if search_results.get('name') and len(search_results['name']) > len(product['name']):
                        product['name'] = search_results['name']

                    # Get images or use default ones
                    images = search_results.get('images', [])
                    if not images:
                        images = self.generate_default_images(row['Material Description'])

                    product.update({
                        'description': search_results.get('description', f"High-quality {row['Material Description']} from Bosch. Professional grade tool suitable for various applications."),
                        'rating': search_results.get('rating', '4.0'),
                        'brand': search_results.get('brand', 'Bosch'),
                        'images': images
                    })
                else:
                    # Default values if no online data found
                    product.update({
                        'description': f"Professional grade {row['Material Description']} from Bosch. High-quality tool designed for durability and performance.",
                        'rating': '4.0',
                        'brand': 'Bosch',
                        'images': self.generate_default_images(row['Material Description'])
                    })
                
                products.append(product)
                
                # Add delay to avoid being blocked
                time.sleep(random.uniform(1, 3))
                
                # Limit to first 10 products for testing
                if len(products) >= 10:
                    break
            
            return products
            
        except Exception as e:
            print(f"Error processing CSV: {e}")
            return []
    
    def save_products_json(self, products: List[Dict], output_file: str = 'products.json'):
        """Save products to JSON file"""
        output_data = {'products': products}
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
        
        print(f"Saved {len(products)} products to {output_file}")

def main():
    scraper = ProductScraper()
    
    # Process the CSV file
    csv_file = "GT Pricelist effective 1st March shared.csv"
    products = scraper.process_csv_products(csv_file)
    
    # Save to JSON file
    scraper.save_products_json(products)
    
    # Print sample output
    if products:
        print("\nSample product:")
        print(json.dumps(products[0], indent=2, ensure_ascii=False))

if __name__ == "__main__":
    main()
