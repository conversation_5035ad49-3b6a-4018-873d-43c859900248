"use client";

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ArrowUp, ShoppingCart, Heart, Search } from "lucide-react";
import { But<PERSON> } from "./button";
import Link from "next/link";

const FloatingActionButton: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
        setIsExpanded(false);
      }
    };

    window.addEventListener("scroll", toggleVisibility);
    return () => window.removeEventListener("scroll", toggleVisibility);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  const fabVariants = {
    hidden: { 
      opacity: 0, 
      scale: 0,
      y: 100
    },
    visible: { 
      opacity: 1, 
      scale: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 260,
        damping: 20
      }
    }
  };

  const menuVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.8 },
    visible: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 20
      }
    }
  };

  const actionButtons = [
    {
      icon: Search,
      label: "Search",
      href: "/shop",
      color: "from-blue-500 to-blue-600"
    },
    {
      icon: Heart,
      label: "Wishlist",
      href: "/wishlist",
      color: "from-pink-500 to-pink-600"
    },
    {
      icon: ShoppingCart,
      label: "Cart",
      href: "/cart",
      color: "from-green-500 to-green-600"
    }
  ];

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="fixed bottom-6 right-6 z-50"
          variants={fabVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
        >
          {/* Action Menu */}
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                className="absolute bottom-16 right-0 space-y-3"
                variants={menuVariants}
                initial="hidden"
                animate="visible"
                exit="hidden"
              >
                {actionButtons.map((action, index) => (
                  <motion.div
                    key={action.label}
                    variants={itemVariants}
                  >
                    <Link href={action.href}>
                      <Button
                        className={`w-12 h-12 rounded-full bg-gradient-to-r ${action.color} hover:scale-110 transition-transform duration-200 shadow-lg hover:shadow-xl glass-dark`}
                        onClick={() => setIsExpanded(false)}
                        aria-label={action.label}
                      >
                        <action.icon className="w-5 h-5 text-white" />
                      </Button>
                    </Link>
                  </motion.div>
                ))}
              </motion.div>
            )}
          </AnimatePresence>

          {/* Main FAB */}
          <motion.div
            className="relative"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <Button
              onClick={isExpanded ? scrollToTop : () => setIsExpanded(!isExpanded)}
              className="w-14 h-14 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 shadow-2xl glass-dark border border-white/20"
              aria-label={isExpanded ? "Scroll to top" : "Open menu"}
            >
              <motion.div
                animate={{ rotate: isExpanded ? 45 : 0 }}
                transition={{ duration: 0.2 }}
              >
                {isExpanded ? (
                  <ArrowUp className="w-6 h-6 text-white" />
                ) : (
                  <div className="w-6 h-6 flex flex-col justify-center items-center space-y-1">
                    <div className="w-4 h-0.5 bg-white rounded"></div>
                    <div className="w-4 h-0.5 bg-white rounded"></div>
                    <div className="w-4 h-0.5 bg-white rounded"></div>
                  </div>
                )}
              </motion.div>
            </Button>

            {/* Ripple effect */}
            <motion.div
              className="absolute inset-0 rounded-full bg-white/20"
              initial={{ scale: 0, opacity: 0.5 }}
              animate={{ scale: 2, opacity: 0 }}
              transition={{ duration: 1.5, repeat: Infinity }}
            />
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default FloatingActionButton;
