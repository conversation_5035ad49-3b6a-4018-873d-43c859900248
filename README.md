# Product Scraper for GT Pricelist

This Python script extracts product information from the GT Pricelist CSV file and enriches it with images and descriptions from e-commerce websites.

## Features

- ✅ Reads product data from CSV file
- ✅ Searches for product images and descriptions on Flipkart and Amazon
- ✅ Calculates discount percentages
- ✅ Categorizes products automatically
- ✅ Generates JSON output in the requested format
- ✅ Handles errors gracefully with fallback options
- ✅ Rate limiting to avoid being blocked by websites

## Requirements

- Python 3.7 or higher
- Internet connection for web scraping

## Installation

1. Install required packages:
```bash
pip install -r requirements.txt
```

## Usage

### Quick Start
```bash
python run_scraper.py
```

### Advanced Usage
```python
from product_scraper import ProductScraper

# Initialize scraper
scraper = ProductScraper()

# Process CSV file
products = scraper.process_csv_products("GT Pricelist effective 1st March shared.csv")

# Save to JSON
scraper.save_products_json(products, "my_products.json")
```

## Output Format

The script generates a JSON file with the following structure:

```json
{
  "products": [
    {
      "name": "GSR 120-LI + 2x2.0Ah",
      "sell_price": 7321.90,
      "cost_price": 12900.00,
      "discount": 43.2,
      "description": "Professional grade drill driver with battery pack...",
      "rating": "4.2",
      "brand": "Bosch",
      "category": "Power Tools",
      "images": [
        "https://example.com/image1.jpg",
        "https://example.com/image2.jpg"
      ],
      "part_no": "06019G80F0",
      "hsn_code": "84672100",
      "power_source": "Battery",
      "user_group": "BPR"
    }
  ]
}
```

## Configuration

### Modify Processing Limits
To process more or fewer products, edit the limit in `product_scraper.py`:

```python
# In process_csv_products method
if len(products) >= 10:  # Change this number
    break
```

### Add More E-commerce Sites
You can extend the scraper by adding more search methods:

```python
def search_new_site(self, product_name: str) -> Dict:
    # Your implementation here
    pass
```

## Troubleshooting

### Common Issues

1. **No images found**: The script will use placeholder images if no real images are found
2. **Rate limiting**: The script includes delays between requests to avoid being blocked
3. **Network errors**: Check your internet connection and try again

### Error Messages

- `CSV file not found`: Make sure the CSV file is in the same directory
- `No products processed`: This usually indicates network issues or website blocking

## File Structure

```
├── product_scraper.py          # Main scraper class
├── run_scraper.py             # Simple runner script
├── requirements.txt           # Python dependencies
├── README.md                  # This file
├── GT Pricelist effective 1st March shared.csv  # Input CSV file
└── products_output.json       # Generated output file
```

## Notes

- The script is designed to be respectful to websites with appropriate delays
- Some websites may block automated requests - this is normal
- The script will try multiple sources and fallback to default values
- Images are limited to 8 per product to keep file sizes manageable

## Customization

You can customize the scraper by:

1. **Adding more product fields**: Modify the product dictionary in `process_csv_products`
2. **Changing categorization**: Update the `categorize_product` method
3. **Adding more image sources**: Implement additional search methods
4. **Modifying output format**: Change the `save_products_json` method

## Legal Notice

This script is for educational and personal use only. Please respect the terms of service of the websites you're scraping and use appropriate rate limiting.
