"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_components_home_hero-carousel_tsx"],{

/***/ "(app-pages-browser)/./components/home/<USER>":
/*!*************************************************!*\
  !*** ./components/home/<USER>
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AmazonStyleCarousel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction AmazonStyleCarousel(param) {\n    let { slides, autoplayInterval = 5000, className } = param;\n    _s();\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAutoPlaying, setIsAutoPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const autoPlayRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Reset autoplay timer when slide changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AmazonStyleCarousel.useEffect\": ()=>{\n            if (isAutoPlaying) {\n                if (autoPlayRef.current) {\n                    clearInterval(autoPlayRef.current);\n                }\n                autoPlayRef.current = setInterval({\n                    \"AmazonStyleCarousel.useEffect\": ()=>{\n                        setCurrentSlide({\n                            \"AmazonStyleCarousel.useEffect\": (prev)=>(prev + 1) % slides.length\n                        }[\"AmazonStyleCarousel.useEffect\"]);\n                    }\n                }[\"AmazonStyleCarousel.useEffect\"], autoplayInterval);\n            }\n            return ({\n                \"AmazonStyleCarousel.useEffect\": ()=>{\n                    if (autoPlayRef.current) {\n                        clearInterval(autoPlayRef.current);\n                    }\n                }\n            })[\"AmazonStyleCarousel.useEffect\"];\n        }\n    }[\"AmazonStyleCarousel.useEffect\"], [\n        currentSlide,\n        isAutoPlaying,\n        autoplayInterval,\n        slides.length\n    ]);\n    // Pause autoplay on hover\n    const pauseAutoPlay = ()=>setIsAutoPlaying(false);\n    const resumeAutoPlay = ()=>setIsAutoPlaying(true);\n    const goToSlide = (index)=>{\n        setCurrentSlide(index);\n    };\n    const goToPrevSlide = ()=>{\n        setCurrentSlide((prev)=>prev === 0 ? slides.length - 1 : prev - 1);\n    };\n    const goToNextSlide = ()=>{\n        setCurrentSlide((prev)=>(prev + 1) % slides.length);\n    };\n    // Handle touch events for mobile swipe\n    const handleTouchStart = (e)=>{\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const distance = touchStart - touchEnd;\n        const isLeftSwipe = distance > 50;\n        const isRightSwipe = distance < -50;\n        if (isLeftSwipe) {\n            goToNextSlide();\n        } else if (isRightSwipe) {\n            goToPrevSlide();\n        }\n        setTouchStart(null);\n        setTouchEnd(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"relative overflow-hidden rounded-xl shadow-lg mt-2 bg-transparent from-theme-header to-theme-header/90\", className),\n        onMouseEnter: pauseAutoPlay,\n        onMouseLeave: resumeAutoPlay,\n        ref: containerRef,\n        onTouchStart: handleTouchStart,\n        onTouchMove: handleTouchMove,\n        onTouchEnd: handleTouchEnd,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-opacity-10 z-0 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 opacity-10 bg-[radial-gradient(#ffffff33_1px,transparent_1px)] bg-[size:20px_20px]\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex transition-transform duration-700 ease-out h-[280px] xs:h-[320px] sm:h-[380px] md:h-[420px] lg:h-[480px] xl:h-[520px]\",\n                style: {\n                    transform: \"translateX(-\".concat(currentSlide * 100, \"%)\")\n                },\n                children: slides.map((slide, index)=>{\n                    var _slide_brand, _slide_brand1, _slide_brand2, _slide_brand3, _slide_brand4;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-w-full relative group cursor-pointer\",\n                        onClick: ()=>{\n                            try {\n                                window.location.href = slide.link || \"/shop\";\n                            } catch (error) {\n                                console.error(\"Navigation error:\", error);\n                                window.location.href = \"/shop\";\n                            }\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-[#8a6f4d]/20 to-[#d9c3a9]/10\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 right-0 bottom-0 w-[45%] xs:w-[42%] sm:w-[45%] md:w-[50%] flex items-center justify-center p-2 xs:p-3 sm:p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-full h-full max-w-[95%] max-h-[85%] xs:max-h-[90%]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: slide.image || '/home/<USER>',\n                                        alt: slide.title || 'Product Image',\n                                        fill: true,\n                                        sizes: \"(max-width: 640px) 50vw, (max-width: 768px) 45vw, (max-width: 1024px) 50vw, 50vw\",\n                                        className: \"object-contain object-center\",\n                                        priority: index === 0,\n                                        onError: (e)=>{\n                                            // Fallback to a placeholder if image fails to load\n                                            const imgElement = e.currentTarget;\n                                            if (!imgElement.src.includes('placeholder') && !imgElement.src.includes('image-1.png')) {\n                                                imgElement.src = '/home/<USER>';\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-black/50 via-black/30 to-transparent\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-[url('/patterns/dot-pattern.svg')] bg-repeat opacity-10\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white p-2 xs:p-3 sm:p-4 md:p-6 lg:p-8 xl:p-12 w-[55%] xs:w-[58%] sm:w-[55%] md:w-[50%] lg:w-[45%] xl:w-[40%]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-1.5 xs:gap-2 sm:gap-3 mb-1 xs:mb-1.5 sm:mb-2 md:mb-3\",\n                                            children: [\n                                                slide.brand && typeof slide.brand !== 'string' && (((_slide_brand = slide.brand) === null || _slide_brand === void 0 ? void 0 : _slide_brand.image_url) || ((_slide_brand1 = slide.brand) === null || _slide_brand1 === void 0 ? void 0 : _slide_brand1.image)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 mt-0.5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-6 h-6 xs:w-8 xs:h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 lg:w-14 lg:h-14 overflow-hidden rounded-md border border-white/80 shadow-lg bg-white flex items-center justify-center p-0.5\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: ((_slide_brand2 = slide.brand) === null || _slide_brand2 === void 0 ? void 0 : _slide_brand2.image_url) || \"\".concat(\"http://localhost:8000\" || 0).concat((_slide_brand3 = slide.brand) === null || _slide_brand3 === void 0 ? void 0 : _slide_brand3.image),\n                                                            alt: \"\".concat((_slide_brand4 = slide.brand) === null || _slide_brand4 === void 0 ? void 0 : _slide_brand4.name, \" logo\"),\n                                                            className: \"max-w-full max-h-full object-contain\",\n                                                            onError: (e)=>{\n                                                                // Hide the image on error\n                                                                const imgElement = e.currentTarget;\n                                                                imgElement.style.display = 'none';\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-sm xs:text-base sm:text-lg md:text-xl lg:text-2xl xl:text-3xl 2xl:text-4xl font-bold   [text-shadow:_0_1px_3px_rgb(0_0_0_/_50%)] leading-tight flex-1 pr-1\",\n                                                    children: slide.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs xs:text-xs sm:text-sm md:text-base lg:text-lg mb-1.5 xs:mb-2 sm:mb-3 md:mb-4 text-white/90   [text-shadow:_0_1px_2px_rgb(0_0_0_/_30%)] line-clamp-2 leading-snug max-w-[95%] break-words\",\n                                            children: slide.subtitle\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, this),\n                                        slide.code && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"inline-block bg-white/20 backdrop-blur-sm px-2 xs:px-2.5 sm:px-3 py-0.5 xs:py-1 rounded-full   text-xs xs:text-xs sm:text-sm md:text-base font-medium mb-1.5 xs:mb-2 sm:mb-3   [text-shadow:_0_1px_1px_rgb(0_0_0_/_20%)] whitespace-nowrap\",\n                                            children: slide.code\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1.5 xs:mt-2 sm:mt-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                href: slide.link || \"/shop\",\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                // Additional error handling can be added here if needed\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    className: \"bg-[#2ECC71] hover:bg-[#27AE60] text-white font-medium   px-2.5 xs:px-3 sm:px-4 md:px-6 py-1 xs:py-1.5 sm:py-2 rounded-full   transition-all duration-300 hover:shadow-lg hover:scale-105   text-xs xs:text-xs sm:text-sm md:text-base whitespace-nowrap\",\n                                                    children: slide.cta || \"Shop Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-0 left-0 right-[40%] xs:right-[42%] sm:right-[45%] md:right-[50%] p-1.5 xs:p-2 sm:p-3 bg-gradient-to-t from-black/60 to-transparent\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white text-xs xs:text-xs sm:text-sm\",\n                                    children: slide.specs && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mb-0.5 [text-shadow:_0_1px_1px_rgb(0_0_0_/_30%)] leading-tight break-words max-w-full opacity-90\",\n                                        children: slide.specs\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: (e)=>{\n                    e.stopPropagation();\n                    goToPrevSlide();\n                },\n                className: \"absolute left-2 xs:left-4 top-1/2 -translate-y-1/2 w-8 h-8 xs:w-10 xs:h-10 items-center justify-center   bg-theme-accent-primary hover:bg-theme-accent-hover text-white rounded-full shadow-md z-10 transition-all duration-300   hover:shadow-lg hover:scale-110 hidden xs:inline-flex\",\n                \"aria-label\": \"Previous slide\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 xs:h-6 xs:w-6 text-white transition-transform duration-300 hover:-translate-x-0.5\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                lineNumber: 244,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: (e)=>{\n                    e.stopPropagation();\n                    goToNextSlide();\n                },\n                className: \"absolute right-2 xs:right-4 top-1/2 -translate-y-1/2 w-8 h-8 xs:w-10 xs:h-10 items-center justify-center   bg-theme-accent-primary hover:bg-theme-accent-hover text-white rounded-full shadow-md z-10 transition-all duration-300   hover:shadow-lg hover:scale-110 hidden xs:inline-flex\",\n                \"aria-label\": \"Next slide\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 xs:h-6 xs:w-6 text-white transition-transform duration-300 hover:translate-x-0.5\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-2 xs:bottom-4 left-1/2 -translate-x-1/2 flex gap-1 xs:gap-2 z-10\",\n                children: slides.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>goToSlide(index),\n                        className: \"w-2 h-2 xs:w-3 xs:h-3 rounded-full transition-all duration-300 \".concat(index === currentSlide ? \"bg-theme-accent-secondary scale-110\" : \"bg-white/50 hover:bg-white/70 hover:scale-105\"),\n                        \"aria-label\": \"Go to slide \".concat(index + 1)\n                    }, index, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n_s(AmazonStyleCarousel, \"q+8ilV0we8mV9YlTA9aGx5Xg1EA=\");\n_c = AmazonStyleCarousel;\nvar _c;\n$RefreshReg$(_c, \"AmazonStyleCarousel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/home/<USER>"));

/***/ }),

/***/ "(app-pages-browser)/./components/home/<USER>":
/*!*******************************************!*\
  !*** ./components/home/<USER>
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HeroCarousel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AmazonStyleCarousel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AmazonStyleCarousel */ \"(app-pages-browser)/./components/home/<USER>");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useApi */ \"(app-pages-browser)/./hooks/useApi.ts\");\n// \"use client\";\n// import { useState, useEffect } from \"react\";\n// import Image from \"next/image\";\n// import { ChevronLeft, ChevronRight } from \"lucide-react\";\n// export default function HeroCarousel() {\n//   const [current, setCurrent] = useState(0);\n//   useEffect(() => {\n//     const timer = setInterval(() => {\n//       setCurrent((prev) => (prev + 1) % slides.length);\n//     }, 5000);\n//     return () => clearInterval(timer);\n//   }, []);\n//   const prev = () => {\n//     setCurrent((curr) => (curr === 0 ? slides.length - 1 : curr - 1));\n//   };\n//   const next = () => {\n//     setCurrent((curr) => (curr + 1) % slides.length);\n//   };\n//   return (\n//     <div className=\"relative overflow-hidden\">\n//       <div\n//         className=\"flex transition-transform duration-500 ease-out\"\n//         style={{ transform: `translateX(-${current * 100}%)` }}\n//       >\n//         {slides.map((slide, index) => (\n//           <div\n//             key={index}\n//             className=\"min-w-full relative h-[400px] md:h-[600px]\"\n//           >\n//             <Image\n//               src={slide.image || \"/placeholder.svg\"}\n//               alt={slide.title}\n//               fill\n//               className=\"object-cover\"\n//               priority={index === 0}\n//             />\n//             <div className=\"absolute inset-0 flex items-center justify-center\">\n//               <div className=\"text-center\">\n//                 <h2 className=\"text-4xl md:text-6xl font-bold mb-4\">\n//                   {slide.title}\n//                 </h2>\n//                 <p className=\"text-xl md:text-2xl mb-2\">{slide.subtitle}</p>\n//                 {slide.code && (\n//                   <p className=\"text-lg md:text-xl font-medium\">{slide.code}</p>\n//                 )}\n//               </div>\n//             </div>\n//           </div>\n//         ))}\n//       </div>\n//       <button\n//         onClick={prev}\n//         className=\"absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 flex items-center justify-center bg-white/80 rounded-full shadow-md\"\n//       >\n//         <ChevronLeft className=\"h-6 w-6\" />\n//       </button>\n//       <button\n//         onClick={next}\n//         className=\"absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 flex items-center justify-center bg-white/80 rounded-full shadow-md\"\n//       >\n//         <ChevronRight className=\"h-6 w-6\" />\n//       </button>\n//       <div className=\"absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2\">\n//         {slides.map((_, index) => (\n//           <button\n//             key={index}\n//             onClick={() => setCurrent(index)}\n//             className={`w-2 h-2 rounded-full transition-colors ${\n//               index === current ? \"bg-white\" : \"bg-white/50\"\n//             }`}\n//           />\n//         ))}\n//       </div>\n//     </div>\n//   );\n// }\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Fallback slides in case API fails\nconst fallbackSlides = [\n    {\n        image: \"/home/<USER>",\n        title: \"PRIMA Smart Lock\",\n        subtitle: \"Advanced security with fingerprint and PIN access\",\n        code: \"NEW ARRIVAL\",\n        cta: \"Shop Now\",\n        link: \"/shop\",\n        specs: \"Lock Size (inches): 9.6 x 5.6 • Min. Wooden Door Thickness: 3.2 cm Build\",\n        brand: \"PRIMA\"\n    },\n    {\n        image: \"/home/<USER>",\n        title: \"Qubo Smart Door Lock\",\n        subtitle: \"Keyless entry with advanced security features\",\n        cta: \"Explore Collection\",\n        link: \"/shop\",\n        specs: \"Compatible with standard door sizes • Multiple access methods\",\n        brand: \"Qubo\"\n    }\n];\nfunction HeroCarousel() {\n    _s();\n    // Use a ref to track if we've already fetched data to prevent continuous API calls\n    const dataFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const { data: featuredProducts, loading, read } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL || '');\n    const [slides, setSlides] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HeroCarousel.useEffect\": ()=>{\n            // Skip if we've already processed the data\n            if (dataFetchedRef.current && slides.length > 0) {\n                setIsLoading(false);\n                return;\n            }\n            const fetchFeaturedProducts = {\n                \"HeroCarousel.useEffect.fetchFeaturedProducts\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        // Only fetch if we don't already have featuredProducts data\n                        let productsArray = [];\n                        if (featuredProducts) {\n                            // Use existing data if available\n                            if (Array.isArray(featuredProducts)) {\n                                productsArray = featuredProducts;\n                            } else if (featuredProducts.results && Array.isArray(featuredProducts.results)) {\n                                productsArray = featuredProducts.results;\n                            } else if (featuredProducts.products && Array.isArray(featuredProducts.products)) {\n                                productsArray = featuredProducts.products;\n                            }\n                        } else {\n                            // Fetch only if necessary\n                            const result = await read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.FUTURED_PRODUCTS);\n                            // Process the API response\n                            if (Array.isArray(result)) {\n                                productsArray = result;\n                            } else if (result && result.results && Array.isArray(result.results)) {\n                                productsArray = result.results;\n                            } else if (result && result.products && Array.isArray(result.products)) {\n                                productsArray = result.products;\n                            }\n                        }\n                        // Take top 5 products\n                        const topProducts = productsArray.slice(0, 5);\n                        if (topProducts.length > 0) {\n                            // Transform products into slides\n                            const productSlides = topProducts.map({\n                                \"HeroCarousel.useEffect.fetchFeaturedProducts.productSlides\": (product)=>{\n                                    // Ensure we have a valid image URL\n                                    let imageUrl = '/home/<USER>'; // Default fallback image\n                                    if (product.image) {\n                                        if (product.image.startsWith('http')) {\n                                            imageUrl = product.image;\n                                        } else if (_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL) {\n                                            // Make sure the image path is properly formatted\n                                            const imagePath = product.image.startsWith('/') ? product.image : \"/\".concat(product.image);\n                                            imageUrl = _constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL + imagePath;\n                                        }\n                                    }\n                                    // Clean up product description to avoid HTML tags\n                                    let cleanDescription = product.description || \"Explore our premium collection\";\n                                    cleanDescription = cleanDescription.replace(/<[^>]*>?/gm, '');\n                                    // Create specifications string from product attributes\n                                    let specs = '';\n                                    if (product.size) specs += \"Size: \".concat(product.size, \" \");\n                                    if (product.dimensions) specs += \"Dimensions: \".concat(product.dimensions, \" \");\n                                    if (product.weight) specs += \"Weight: \".concat(product.weight, \" \");\n                                    if (product.material) specs += \"Material: \".concat(product.material, \" \");\n                                    if (product.color) specs += \"Color: \".concat(product.color, \" \");\n                                    // For products like the ones in the image (door locks, etc.)\n                                    if (product.category && product.category.name && product.category.name.toLowerCase().includes('lock')) {\n                                        specs = \"Lock Size: \".concat(product.dimensions || '9.6 x 5.6', \" • Min. Door Thickness: \").concat(product.min_thickness || '3.2 cm', \" Build\");\n                                    }\n                                    return {\n                                        image: imageUrl,\n                                        title: product.name || \"Featured Product\",\n                                        subtitle: cleanDescription.substring(0, 100),\n                                        code: product.discount_percentage ? \"SAVE \".concat(product.discount_percentage, \"%\") : undefined,\n                                        cta: \"Shop Now\",\n                                        link: \"/product/\".concat(product.slug),\n                                        specs: specs,\n                                        brand: product.brand || undefined\n                                    };\n                                }\n                            }[\"HeroCarousel.useEffect.fetchFeaturedProducts.productSlides\"]);\n                            setSlides(productSlides);\n                        } else {\n                            // Use fallback slides if no products found\n                            setSlides(fallbackSlides);\n                        }\n                        // Mark as fetched to prevent continuous API calls\n                        dataFetchedRef.current = true;\n                    } catch (error) {\n                        console.error(\"Error fetching featured products for carousel:\", error);\n                        setSlides(fallbackSlides);\n                        dataFetchedRef.current = true; // Mark as fetched even on error to prevent continuous retries\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"HeroCarousel.useEffect.fetchFeaturedProducts\"];\n            fetchFeaturedProducts();\n        }\n    }[\"HeroCarousel.useEffect\"], [\n        featuredProducts,\n        read\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full mb-4 sm:mb-8 pt-2\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"rounded-xl overflow-hidden shadow-lg h-[250px] xs:h-[300px] sm:h-[350px] md:h-[400px] lg:h-[450px] xl:h-[500px] bg-gray-100 animate-pulse\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full w-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 rounded-full border-4 border-theme-accent-primary border-t-transparent animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\hero-carousel.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\hero-carousel.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\hero-carousel.tsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\hero-carousel.tsx\",\n            lineNumber: 234,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full mb-4 sm:mb-8 pt-2\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AmazonStyleCarousel__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            slides: slides.length > 0 ? slides : fallbackSlides,\n            autoplayInterval: 5000,\n            className: \"rounded-xl overflow-hidden shadow-lg transform transition-all duration-700 hover:shadow-xl\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\hero-carousel.tsx\",\n            lineNumber: 246,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\hero-carousel.tsx\",\n        lineNumber: 245,\n        columnNumber: 5\n    }, this);\n}\n_s(HeroCarousel, \"8SZBhfWzaJkVWkPnvRScigGe2KY=\", false, function() {\n    return [\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n_c = HeroCarousel;\nvar _c;\n$RefreshReg$(_c, \"HeroCarousel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/home/<USER>"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevron-left.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChevronLeft)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst ChevronLeft = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ChevronLeft\", [\n    [\n        \"path\",\n        {\n            d: \"m15 18-6-6 6-6\",\n            key: \"1wnfg3\"\n        }\n    ]\n]);\n //# sourceMappingURL=chevron-left.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hldnJvbi1sZWZ0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBYU0sb0JBQWMsZ0VBQWdCLENBQUMsYUFBZTtJQUNsRDtRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsQ0FBa0I7WUFBQSxLQUFLO1FBQUEsQ0FBVTtLQUFBO0NBQ2hEIiwic291cmNlcyI6WyJEOlxcc3JjXFxpY29uc1xcY2hldnJvbi1sZWZ0LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgQ2hldnJvbkxlZnRcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKdE1UVWdNVGd0TmkwMklEWXROaUlnTHo0S1BDOXpkbWMrQ2c9PSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvY2hldnJvbi1sZWZ0XG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgQ2hldnJvbkxlZnQgPSBjcmVhdGVMdWNpZGVJY29uKCdDaGV2cm9uTGVmdCcsIFtcbiAgWydwYXRoJywgeyBkOiAnbTE1IDE4LTYtNiA2LTYnLCBrZXk6ICcxd25mZzMnIH1dLFxuXSk7XG5cbmV4cG9ydCBkZWZhdWx0IENoZXZyb25MZWZ0O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js":
/*!*******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevron-right.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChevronRight)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst ChevronRight = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ChevronRight\", [\n    [\n        \"path\",\n        {\n            d: \"m9 18 6-6-6-6\",\n            key: \"mthhwq\"\n        }\n    ]\n]);\n //# sourceMappingURL=chevron-right.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hldnJvbi1yaWdodC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQWFNLHFCQUFlLGdFQUFnQixDQUFDLGNBQWdCO0lBQ3BEO1FBQUMsTUFBUTtRQUFBO1lBQUUsR0FBRyxDQUFpQjtZQUFBLEtBQUs7UUFBQSxDQUFVO0tBQUE7Q0FDL0MiLCJzb3VyY2VzIjpbIkQ6XFxzcmNcXGljb25zXFxjaGV2cm9uLXJpZ2h0LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgQ2hldnJvblJpZ2h0XG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSnRPU0F4T0NBMkxUWXROaTAySWlBdlBnbzhMM04yWno0SykgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvY2hldnJvbi1yaWdodFxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IENoZXZyb25SaWdodCA9IGNyZWF0ZUx1Y2lkZUljb24oJ0NoZXZyb25SaWdodCcsIFtcbiAgWydwYXRoJywgeyBkOiAnbTkgMTggNi02LTYtNicsIGtleTogJ210aGh3cScgfV0sXG5dKTtcblxuZXhwb3J0IGRlZmF1bHQgQ2hldnJvblJpZ2h0O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\n"));

/***/ })

}]);