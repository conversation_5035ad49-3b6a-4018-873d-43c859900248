import json

json_file_path = 'products_output.json'

def remove_non_bosch_products(json_file_path):
    try:
        with open(json_file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
        
        products = data.get('products', [])
        bosch_products = [product for product in products if 'bosch' in product['name'].lower()]
        print(f"Found {len(bosch_products)} Bosch products out of {len(products)} total products")
        
        if not bosch_products:
            print("No Bosch products found.")
            return
        
        output_data = {"products": bosch_products}
        
        with open('bosch_products_output.json', 'w', encoding='utf-8') as output_file:
            print(f"Saving {len(bosch_products)} Bosch products to 'bosch_products_output.json'")
            json.dump(output_data, output_file, ensure_ascii=False, indent=2)
        
        print(f"Filtered {len(bosch_products)} Bosch products and saved to 'bosch_products_output.json'")
    
    except FileNotFoundError:
        print(f"File {json_file_path} not found.")
    except json.JSONDecodeError:
        print("Error decoding JSON from the file.")
    except Exception as e:
        print(f"An error occurred: {str(e)}")

remove_non_bosch_products(json_file_path)