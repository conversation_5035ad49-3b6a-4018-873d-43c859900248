# Product Scraper for GT Pricelist

This project contains Python scripts to extract product information from the GT Pricelist CSV file and fetch real product images and descriptions from e-commerce websites.

## Files

1. **`enhanced_product_scraper.py`** - Main scraper with advanced features
2. **`product_scraper.py`** - Basic scraper (original version)
3. **`run_product_scraper.py`** - Easy-to-use runner script
4. **`requirements.txt`** - Python dependencies

## Features

- ✅ Reads CSV files with multiple encoding support
- ✅ Fetches real product images from Flipkart
- ✅ Extracts detailed product descriptions
- ✅ Calculates discount percentages
- ✅ Categorizes products automatically
- ✅ Generates high-quality image URLs
- ✅ Handles rate limiting and errors gracefully
- ✅ Outputs data in the requested JSON format

## Installation

1. Install Python dependencies:
```bash
pip install -r requirements.txt
```

## Usage

### Quick Start

Run the enhanced scraper with default settings:
```bash
python enhanced_product_scraper.py
```

### Using the Runner Script

The runner script provides more options:

```bash
# Basic usage
python run_product_scraper.py

# Process more products
python run_product_scraper.py --max-products 50

# Custom output file
python run_product_scraper.py --output my_products.json

# Show sample product after processing
python run_product_scraper.py --sample

# Custom CSV file
python run_product_scraper.py --csv "my_pricelist.csv" --max-products 30
```

### Command Line Options

- `--csv`: Path to CSV file (default: GT Pricelist effective 1st March shared.csv)
- `--output`: Output JSON file name (default: products_output.json)
- `--max-products`: Maximum products to process (default: 20)
- `--sample`: Show sample product after processing

## Output Format

The script generates JSON in the exact format you requested:

```json
{
  "products": [
    {
      "name": "BOSCH GSB 120-LI, with 2xGBA 12V 2.0Ah Battery...",
      "sell_price": 7321.9,
      "cost_price": 12900.0,
      "discount": 43.2,
      "description": "Professional grade tool...",
      "rating": "4.0",
      "brand": "Bosch",
      "category": "Power Tools",
      "images": [
        "https://rukminim2.flixcart.com/image/1080/1080/...",
        "https://rukminim2.flixcart.com/image/1080/1080/...",
        "..."
      ]
    }
  ]
}
```

## How It Works

1. **CSV Reading**: Reads the pricelist CSV with automatic encoding detection
2. **Data Extraction**: Extracts product names, prices, and basic info
3. **Web Scraping**: Searches Flipkart for matching products
4. **Image Processing**: Converts images to high-quality URLs (1080x1080)
5. **Data Enhancement**: Merges CSV data with scraped information
6. **JSON Generation**: Outputs in the requested format

## Data Sources

The scraper attempts to fetch data from multiple sources in order:

1. **Flipkart** - Primary source for images and descriptions
2. **Bosch Official Website** - Fallback for official product info
3. **Google Shopping** - Additional fallback
4. **Generated Placeholders** - If no real images found

## Rate Limiting

The scraper includes built-in rate limiting:
- 2-5 second delays between requests
- Multiple retry attempts
- Graceful error handling

## Troubleshooting

### Common Issues

1. **No images found**: Some products may not be available online
2. **Slow processing**: Web scraping takes time due to rate limiting
3. **Encoding errors**: The script handles multiple encodings automatically

### Tips

- Start with a small number of products (`--max-products 10`) to test
- Check your internet connection if scraping fails
- Some products may only have placeholder images if not found online

## Example Output

After running the script, you'll see:

```
🚀 Starting Enhanced Product Scraper
📁 CSV File: GT Pricelist effective 1st March shared.csv
📄 Output File: products_output.json
🔢 Max Products: 15
--------------------------------------------------
✅ Successfully processed 15 products
💾 Results saved to products_output.json

📊 Statistics:
Total Products: 15
Products with Real Images: 12
Products with Placeholder Images: 3

📂 Categories:
  Power Tools: 13 products
  Accessories: 2 products
```

## Generated Files

- `products_output.json` - Main output file with product data
- `products.json` - Output from basic scraper (if run)

## Performance

- Processing time: ~3-5 seconds per product
- Success rate: ~80% for finding real images
- Memory usage: Low (processes one product at a time)

## Customization

You can modify the scraper by:

1. Adding new data sources in `enhanced_product_scraper.py`
2. Changing image quality settings
3. Modifying product categorization rules
4. Adding new output formats

## Support

If you encounter issues:

1. Check the console output for error messages
2. Verify the CSV file format matches the expected structure
3. Ensure all dependencies are installed
4. Try reducing the number of products to process

## License

This script is provided as-is for processing the GT Pricelist data.
