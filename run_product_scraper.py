#!/usr/bin/env python3
"""
Product Scraper Runner
This script provides an easy way to run the product scraper with different configurations.
"""

import argparse
import json
import sys
from enhanced_product_scraper import EnhancedProductScraper

def main():
    parser = argparse.ArgumentParser(description='Run the enhanced product scraper')
    parser.add_argument('--csv', default='GT Pricelist effective 1st March shared.csv', 
                       help='Path to the CSV file (default: GT Pricelist effective 1st March shared.csv)')
    parser.add_argument('--output', default='products_output.json', 
                       help='Output JSON file name (default: products_output.json)')
    parser.add_argument('--max-products', type=int, default=20, 
                       help='Maximum number of products to process (default: 20)')
    parser.add_argument('--sample', action='store_true', 
                       help='Show a sample product after processing')
    
    args = parser.parse_args()
    
    print(f"🚀 Starting Enhanced Product Scraper")
    print(f"📁 CSV File: {args.csv}")
    print(f"📄 Output File: {args.output}")
    print(f"🔢 Max Products: {args.max_products}")
    print("-" * 50)
    
    try:
        # Initialize scraper
        scraper = EnhancedProductScraper()
        
        # Process products
        products = scraper.process_csv_enhanced(args.csv, max_products=args.max_products)
        
        if not products:
            print("❌ No products were processed successfully.")
            sys.exit(1)
        
        # Save to JSON
        output_data = {'products': products}
        
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Successfully processed {len(products)} products")
        print(f"💾 Results saved to {args.output}")
        
        # Show sample if requested
        if args.sample and products:
            print("\n📋 Sample Product:")
            print("-" * 30)
            sample = products[0]
            print(f"Name: {sample['name'][:80]}...")
            print(f"Price: ₹{sample['sell_price']}")
            print(f"MRP: ₹{sample['cost_price']}")
            print(f"Discount: {sample['discount']}%")
            print(f"Brand: {sample['brand']}")
            print(f"Category: {sample['category']}")
            print(f"Images: {len(sample['images'])} found")
            if sample['images']:
                print(f"First Image: {sample['images'][0]}")
        
        # Show statistics
        print(f"\n📊 Statistics:")
        print(f"Total Products: {len(products)}")
        
        # Count products with real images (not placeholder)
        real_images = sum(1 for p in products if p['images'] and 'rukminim' in p['images'][0])
        print(f"Products with Real Images: {real_images}")
        print(f"Products with Placeholder Images: {len(products) - real_images}")
        
        # Show categories
        categories = {}
        for product in products:
            cat = product['category']
            categories[cat] = categories.get(cat, 0) + 1
        
        print(f"\n📂 Categories:")
        for cat, count in categories.items():
            print(f"  {cat}: {count} products")
        
    except FileNotFoundError:
        print(f"❌ Error: CSV file '{args.csv}' not found.")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
